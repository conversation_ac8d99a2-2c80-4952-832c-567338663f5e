﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Management;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using Microsoft.Win32;
using PetShikongTools;

namespace Shikong.Pokemon2.PCG
{
    internal class AntiCheat
    {
        internal static string FinalPath =
            Path.Combine(DataProcess.ApplicationDataPath, @"Tencent\Config\p2p_200014357082.ini");

        internal static string FP1 =
            Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), @"txsso.db");

        internal static string FP2 = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            @"u7x5t4e.dll");

        internal static bool AnJian = false;
        private static bool _warning = false;

        internal static void Anti_AnJian()
        {
            bool aj = false;

            Process[] jclb = Process.GetProcesses();
            foreach (Process process in jclb)
            {
                if (process.MainWindowTitle.Contains("按键精灵") || process.ProcessName.Contains("按键精灵"))
                {
                    aj = true;
                }
            }

            string path = string.Empty;


            try
            {
                RegistryKey ajml =
                    Registry.ClassesRoot.CreateSubKey(
                        "WOW6432Node\\CLSID\\{EBEB87A4-E151-4054-AB45-A6E094C5334B}\\LocalServer32");

                if (ajml?.GetValue("") != null)
                {
                    path = ajml.GetValue("").ToString();
                }
                //Console.WriteLine(path);
            }
            catch
            {
                //Console.WriteLine("1");
            }


            if (!string.IsNullOrWhiteSpace(path) && File.Exists(path))
            {
                aj = new NativeMethods.IsInUse().IsFileInUse(path);
            }
        
            if (aj)
            {
                AnJian = true;
                PetProcess.Effect = 0.6;
                LogSystem.JoinLog(LogSystem.EventKind.按键精灵, "Find By TimerTask");
                if (!_warning)
                {
                    DialogResult dr = MessageBox.Show(Res.RM.GetString("按键提示"), Res.RM.GetString("警告"),
                        MessageBoxButtons.OKCancel);

                    if (dr == DialogResult.Cancel)
                    {
                        Tools.ForcedExit("不同意按键条款");
                    }
                    else if (dr == DialogResult.OK)
                    {
                        _warning = true;
                    }
                }
            }

                
        }

        internal void CheckProcess()
        {
            Process[] jclb = Process.GetProcesses();
            int c = 0;
            //检测启动器是否存在
            foreach (Process process in jclb)
            {
                if (process.MainWindowTitle.ToUpper().Contains("CHEATENGINE") || process.MainWindowTitle.ToUpper() == "OLLYDBG"
                    || process.MainWindowTitle.ToUpper() == "OLLYICE" || process.MainWindowTitle.ToUpper() == "MHSCN"
                    || process.MainWindowTitle.ToUpper() == "KNIGHTV" || process.MainWindowTitle == "吾爱破解[LCG]"
                    || process.MainWindowTitle.ToUpper() == "VZLA ENGINE" || process.MainWindowTitle.Contains("金山游侠"))
                {
                    process.Kill();
                    CheatCodeMsg("000（请勿使用内存修改器）");
                    LogSystem.JoinLog(LogSystem.EventKind.作弊检测, "000（请勿使用内存修改器）");
                    PunishmentProcess(0);
                }

                /*if (process.MainWindowTitle.Contains("加速") && process.MainWindowTitle.Contains("单机"))
                {
                    process.Kill();
                    CheatCodeMsg("000（请勿使用变速工具）");
                    PunishmentProcess(0);
                }*/
                //if (/*process.MainWindowTitle.Contains("变速") || */process.MainWindowTitle.ToUpper().Contains("齿轮") || process.MainWindowTitle.ToUpper().Contains("GEARNT"))
                //{
                //    process.Kill();
                //    CheatCodeMsg("000（请勿使用变速工具）");
                //    LogSystem.JoinLog(LogSystem.EventKind.作弊检测, "000（请勿使用变速工具）");
                //    PunishmentProcess(0);
                //}



                /*if ((process.ProcessName.Contains("修改") || process.ProcessName.Contains("作弊") || process.ProcessName.Contains("GM工具")) && (process.ProcessName.Contains("时空口袋") || process.ProcessName.Contains("口袋时空") || process.ProcessName.Contains("时空单机")))
                {
                    process.Kill();
                    CheatCodeMsg("001");
                    PunishmentProcess(1);
                }*/
                if (process.ProcessName.Equals("时空单机启动器"))
                {
                    c = 1;
                }
            }

            if (CheckTime(out _))//这检测变速，但是不关闭游戏，直接在战斗时无效当次掉落
            {
                _noDrop = true;
                LogSystem.JoinLog(LogSystem.EventKind.作弊检测, "000（请勿使用变速工具）");
            }
            // 每 10 秒检查一次是否有外部进程正以可疑权限打开本进程
            //if (CheckMemoryProbe(out var detail))
            //{
            //    // 命中：detail 里包含“谁”在以“什么权限”打开你
            //    Console.WriteLine("⚠️ 检测到疑似内存扫描/操作：\n" + detail);
            //    CheatCodeMsg("000（请勿使用内存修改器）");
            //    LogSystem.JoinLog(LogSystem.EventKind.作弊检测, "000（请勿使用内存修改器）");
            //    PunishmentProcess(0);
            //    // TODO：在这里做你的处理（记录日志 / 提示 / 直接退出 / 降级等）
            //}


            if (c != 1 && DataProcess.AdminMode == 0 && !Program.getDebug())
            {
                SkTools.AutoClosedMsgBox.Show(Res.RM.GetString("进程被破坏"), Res.RM.GetString("警告"), 5000);
                Tools.ForcedExit("监视器进程被破坏");
            }

            if (PetProcess.Effect > 1.0)
            {
                CheatCodeMsg("000（请勿使用内存修改器）");
                LogSystem.JoinLog(LogSystem.EventKind.作弊检测, "000（请勿使用内存修改器）");
                PunishmentProcess(0);
            }

        }

        #region 变速检测

        // 允许误差（±15%）；连续命中次数（默认2次）
        private const double Threshold = 0.15;
        private const int NeedConsecutive = 2;

        // 无偏中断时钟（100ns，自系统启动起，不受 NTP/时区影响）
        [DllImport("kernel32.dll")]
        private static extern bool QueryUnbiasedInterruptTime(out ulong UnbiasedTime100ns);

        // 内部状态
        private static readonly object _lock = new object(); // 线程安全互斥
        private static readonly Stopwatch _sw = Stopwatch.StartNew();
        private static int _lastTick = Environment.TickCount; // 32位，约49.7天回绕
        private static ulong _lastUi;                          // 上次无偏时钟
        private static bool _inited = false;
        private static int _consec = 0;
        private static volatile bool _noDrop = false;          // 报警后一次性标记

        /// <summary>
        /// （推荐）外部入口：根据 t 决定同步/异步调用 CheckTime。
        /// t==0：同步调用；t!=0（如战斗中）：在线程池后台调用，避免卡顿。
        /// onDetected：命中后的回调（注意可能在后台线程）。
        /// </summary>
        public static void CheckTimeWrapper(int t = 0, Action<string> onDetected = null)
        {
            if (t == 0)
            {
                if (CheckTime(out var detail, t))
                    onDetected?.Invoke(detail);
            }
            else
            {
                ThreadPool.QueueUserWorkItem(_ =>
                {
                    if (CheckTime(out var detail, t))
                        onDetected?.Invoke(detail); // 如需更新 UI，请自行切回 UI 线程
                });
            }
        }

        /// <summary>
        /// 放进你的定时器里周期调用（例如 10000ms）。
        /// 命中条件：无偏时钟 与 QPC/Tick 任何一对的比值超出 ±Threshold，且连续 NeedConsecutive 次。
        /// t：0=普通；非0=战斗中（恢复时每次 -1，而非清零）
        /// </summary>
        public static bool CheckTime(out string detail, int t = 0)
        {
            lock (_lock)
            {
                detail = string.Empty;

                // 第一次仅初始化
                if (!_inited)
                {
                    if (!QueryUnbiasedInterruptTime(out _lastUi))
                        return false;

                    _sw.Restart();
                    _lastTick = Environment.TickCount;
                    _inited = true;
                    return false;
                }

                if (!QueryUnbiasedInterruptTime(out var uiNow))
                    return false;

                double uiMs = (uiNow - _lastUi) / 10000.0; // 100ns -> ms
                _lastUi = uiNow;

                double swMs = _sw.Elapsed.TotalMilliseconds;
                _sw.Restart();

                int tickNow = Environment.TickCount;
                double tcMs = unchecked(tickNow - _lastTick);
                _lastTick = tickNow;

                // 倒退直接判异常
                bool abnormal = (uiMs <= 0) || (swMs <= 0) || (tcMs <= 0);
                if (!abnormal)
                {
                    double rQpcUi = SafeDiv(swMs, uiMs); // Stopwatch / Unbiased
                    double rTickUi = SafeDiv(tcMs, uiMs); // Tick      / Unbiased

                    if (OutOfRange(rQpcUi, Threshold) || OutOfRange(rTickUi, Threshold))
                        abnormal = true;

                    // 如需观察可打开日志：
                    // Debug.WriteLine($"rQpcUi={rQpcUi:F3}, rTickUi={rTickUi:F3}, ui={uiMs:F1}ms, qpc={swMs:F1}ms, tick={tcMs:F1}ms");
                }

                if (abnormal)
                {
                    _consec++;
                }
                else
                {
                    // 非异常：根据 t 衰减连续计数
                    if (_consec > 0 && t != 0)
                        _consec = Math.Max(0, _consec - 1);
                    else
                        _consec = 0;

                    _noDrop = false;
                }

                if (_consec >= NeedConsecutive)
                {
                    int hitCount = _consec; // 记录达到阈值时的次数
                    _consec = 0;            // 报警后复位计数，避免刷屏
                    _noDrop = true;

                    detail = $"疑似变速：本地时间源与无偏时钟持续偏离（±{Threshold:P0}，连续{hitCount}次）。";
                    return true;
                }

                return false;
            }
        }

        // 允许范围判断 / 安全除法
        private static bool OutOfRange(double r, double th) => r < (1 - th) || r > (1 + th);
        private static double SafeDiv(double a, double b) => b == 0 ? double.PositiveInfinity : a / b;

        /// <summary>
        /// 获取最近一次是否触发过“禁止掉落”的标志。
        /// 在取值时，会在线程池中异步调用一次 CheckTime(out _, 1) 来更新状态。
        /// 注意：因为是异步调用，本次返回值可能还是上一次的结果。
        /// </summary>
        public static bool NoDrop
        {
            get
            {
                // 异步调用一次 CheckTime
                ThreadPool.QueueUserWorkItem(_ =>
                {
                    try
                    {
                        string detail;                  // 显式声明是 string
                        CheckTime(out detail, 1);       // 不使用 detail 也没关系
                    }
                    catch { /* 避免单次异常中断线程池 */ }
                });

                // 返回当前标志（上一轮结果）
                return _noDrop;
            }
        }


        #endregion




        #region 虚拟机检测
        /// <summary>
        /// 启动时调用的一次性检测：当前系统是否运行在虚拟机/沙箱（Guest）中。
        /// 判定依据采用“虚拟硬件特征”，并使用加权打分机制。
        /// </summary>
        /// <param name="report">返回命中的证据明细（每条一行，便于打印/上报/记录日志）。</param>
        /// <param name="minScore">需要至少多少分才判定为 VM。建议 2（稳健）。</param>
        /// <returns>true = 判定为虚拟机/沙箱来宾；false = 未达到阈值。</returns>
        public static bool IsRunningInVirtualMachine(out string report, int minScore = 2)
        {
            var ev = new List<string>();
            int score = 0;

            // ---------- 1) Hypervisor ----------
            Try(() =>
            {
                using (var qs = new ManagementObjectSearcher("SELECT HypervisorPresent, Model, Manufacturer FROM Win32_ComputerSystem"))
                {
                    foreach (ManagementObject mo in qs.Get())
                    {
                        bool hv = Convert.ToBoolean(mo["HypervisorPresent"] ?? false);
                        string model = (mo["Model"] ?? "").ToString();
                        string mfr = (mo["Manufacturer"] ?? "").ToString();

                        ev.Add($"[WMI] HypervisorPresent={hv}");
                        ev.Add($"[WMI] ComputerSystem: Model=\"{model}\", Manufacturer=\"{mfr}\"");

                        if (hv && string.Equals(model?.Trim(), "Virtual Machine", StringComparison.OrdinalIgnoreCase))
                        {
                            ev.Add("[EVIDENCE] Model=\"Virtual Machine\"（Hyper-V 来宾强特征）");
                            score += 2; // 强特征
                        }
                    }
                }
            });

            // ---------- 2) 主板/BIOS ----------
            var strongVendors = new[]
            {
            "VMware",
            "VirtualBox", "Innotek", "Oracle",
            "Microsoft Corporation",
            "Xen", "XenServer",
            "QEMU", "KVM", "Red Hat",
            "Parallels"
        };

            Try(() =>
            {
                using (var qs = new ManagementObjectSearcher("SELECT Manufacturer, Product FROM Win32_BaseBoard"))
                {
                    foreach (ManagementObject mo in qs.Get())
                    {
                        string mfr = (mo["Manufacturer"] ?? "").ToString();
                        string prod = (mo["Product"] ?? "").ToString();
                        string text = $"{mfr} {prod}";
                        if (ContainsAny(text, strongVendors))
                        {
                            ev.Add($"[EVIDENCE] BaseBoard: {mfr} / {prod} 显示虚拟主板厂商");
                            score += 2; // 强特征
                        }
                        else
                            ev.Add($"[WMI] BaseBoard: {mfr} / {prod}");
                    }
                }

                using (var qs = new ManagementObjectSearcher("SELECT Manufacturer, SMBIOSBIOSVersion, Version FROM Win32_BIOS"))
                {
                    foreach (ManagementObject mo in qs.Get())
                    {
                        string mfr = (mo["Manufacturer"] ?? "").ToString();
                        string ver = (mo["SMBIOSBIOSVersion"] ?? mo["Version"] ?? "").ToString();
                        string text = $"{mfr} {ver}";
                        if (ContainsAny(text, strongVendors))
                        {
                            ev.Add($"[EVIDENCE] BIOS: {mfr} / {ver} 显示虚拟 BIOS 厂商");
                            score += 2; // 强特征
                        }
                        else
                            ev.Add($"[WMI] BIOS: {mfr} / {ver}");
                    }
                }
            });

            // ---------- 3) 显卡 ----------
            Try(() =>
            {
                using (var qs = new ManagementObjectSearcher("SELECT Name FROM Win32_VideoController"))
                {
                    foreach (ManagementObject mo in qs.Get())
                    {
                        string name = (mo["Name"] ?? "").ToString();
                        string up = name.ToUpperInvariant();
                        if (up.Contains("VMWARE") || up.Contains("VIRTUALBOX") || up.Contains("HYPER-V") ||
                            up.Contains("QXL") || up.Contains("PARALLELS") || up.Contains("VIRTIO"))
                        {
                            ev.Add($"[EVIDENCE] VideoController: \"{name}\" 为虚拟显卡特征");
                            score += 2; // 强特征
                        }
                        else
                            ev.Add($"[WMI] VideoController: \"{name}\"");
                    }
                }
            });

            // ---------- 4) 磁盘 ----------
            Try(() =>
            {
                using (var qs = new ManagementObjectSearcher("SELECT Model, Manufacturer, PNPDeviceID FROM Win32_DiskDrive"))
                {
                    foreach (ManagementObject mo in qs.Get())
                    {
                        string model = (mo["Model"] ?? "").ToString();
                        string mfr = (mo["Manufacturer"] ?? "").ToString();
                        string pnp = (mo["PNPDeviceID"] ?? "").ToString();
                        string all = $"{mfr} {model} {pnp}".ToUpperInvariant();

                        if (all.Contains("VMWARE") || all.Contains("VBOX") || all.Contains("VIRTUAL") ||
                            all.Contains("HYPER-V") || all.Contains("QEMU") || all.Contains("KVM") ||
                            all.Contains("VIRTIO") || all.Contains("PARALLELS"))
                        {
                            ev.Add($"[EVIDENCE] DiskDrive: \"{model}\" / \"{pnp}\" 显示虚拟磁盘迹象");
                            score += 2; // 强特征
                        }
                        else
                            ev.Add($"[WMI] DiskDrive: \"{model}\" / \"{pnp}\"");
                    }
                }
            });

            // ---------- 汇总 ----------
            var sb = new StringBuilder();
            sb.AppendLine($"总分：{score} (阈值 {minScore})");
            foreach (var line in ev.Where(l => l.StartsWith("[EVIDENCE]") || l.StartsWith("[WMI] HypervisorPresent")))
                sb.AppendLine(line);

            report = sb.ToString();
            return score >= Math.Max(1, minScore);
        }

        private static bool ContainsAny(string text, IEnumerable<string> keys)
        {
            string up = (text ?? "").ToUpperInvariant();
            foreach (var k in keys)
                if (!string.IsNullOrEmpty(k) && up.Contains(k.ToUpperInvariant()))
                    return true;
            return false;
        }

        private static void Try(Action action)
        {
            try { action(); } catch { /* 忽略单项错误 */ }
        }

        #endregion



        private static void NotYourData()
        {
            SkTools.AutoClosedMsgBox.Show(Res.RM.GetString("私自换存档"), Res.RM.GetString("警告"), 5000);
            Tools.ForcedExit("私自换存档");
        }

        
        /// <summary>
        /// 道具数量检测列表
        /// </summary>
        internal static Dictionary<string, int> CheckList = new Dictionary<string, int>
        {
            //{"2017062404", 50000},//凤凰珠
            //{"20161126", 50000},//伊苏王礼包
            //{"943016", 50000},//法老王的礼包
            //{"2017021601", 100000},//BUFF升级书
            //{"2017021602", 100000},//技能升级书
            //{"2017061401", 5000000},//修炼仙册[嗔]
            //{"2022010607", 1000000},//天使药水-修炼仙册[嗔]
            {"2016102001", 1000000},//每日礼包
            //{"2017092001",10000},//五行点化石
            //{"2017080705",10000},//1E金币券
            {"2017060302",2100000000},//强化石
            {"2016101802",1000 },//滑稽女神之卵
            { "2020120403",100},//★高级宝石自选包★
            { "10002",1},//我的专属称号
            { "2",1},//测试道具
            //{ "2019040805",200},//≮残酷≯之卵大礼包
        };


        internal static void AntiCheat_A()
        {
            UserInfo user = new DataProcess().ReadUserInfo();

            if (Convert.ToInt32(user.版本号) > Convert.ToInt32(DataProcess.Version))
            {
                SkTools.AutoClosedMsgBox.Show(Res.RM.GetString("勿回老版"), Res.RM.GetString("警告"), 5000);
                Tools.ForcedExit("尝试回老版本");
            }

            if (string.IsNullOrEmpty(user.NB1) && string.IsNullOrEmpty(user.NB2) && string.IsNullOrEmpty(user.NB3))
            {
                SkTools.AutoClosedMsgBox.Show(Res.RM.GetString("无硬件信息"), Res.RM.GetString("警告"), 5000);
                Tools.ForcedExit("存档无硬件信息");
            }

            if (string.IsNullOrEmpty(DataProcess.DiskInfo) && string.IsNullOrEmpty(DataProcess.IP))
            {
                DataProcess.GetMachineInfo();
            }


            if (DataProcess.EnvironmentMode == 1)
            {
                try//这里用tyr包起来,防止有人不看信息直接点是
                {
                    if (!DataProcess.IP.Equals(user.NB1))
                    {
                        NotYourData();
                    }
                }
                catch (Exception)
                {
                    MessageBox.Show("不是绑定的云端,或则在自己电脑上选择了是!", "提示");
                    Environment.Exit(0);
                }

            }
            else if (DataProcess.EnvironmentMode == 0 || DataProcess.EnvironmentMode == 2)
            {

                if (!string.IsNullOrEmpty(user.NB1) && DataProcess.DiskInfo == user.NB1
                    || !string.IsNullOrEmpty(user.NB2) && DataProcess.DiskInfo == user.NB2
                    || !string.IsNullOrEmpty(user.NB3) && DataProcess.DiskInfo == user.NB3)
                {

                }
                else
                {
                    NotYourData();
                }

            }
            else
            {
                Tools.ForcedExit("运行环境模式异常");
            }
        }



        internal static void AntiCheat_B()
        {
            try
            {
                new DataProcess().GetPAP();
                new DataProcess().AntiBug1();
                foreach (PropInfo 待检测道具 in DataProcess.PP_List)
                {
                    if (CheckList.ContainsKey(待检测道具.道具类型ID))
                    {
                        if (Convert.ToInt32(待检测道具.道具数量) > CheckList[待检测道具.道具类型ID] && !new DataProcess().getPower())//提供ID[Key]返回value
                        {
                            int s = CheckList[待检测道具.道具类型ID];
                            CheatCodeMsg("01A");
                            LogSystem.JoinLog(LogSystem.EventKind.作弊检测, "01A");
                            PunishmentProcess(2);
                        }
                    }
                    //else
                    //{
                    //    //这里检查道具数量
                    //     if (Convert.ToInt32(待检测道具.道具数量) > 10000000 && !new DataProcess().getPower())
                    //    {
                    //        CheatCodeMsg("01B");
                    //        PunishmentProcess(2);
                    //    }
                    //}

                    /*if (待检测道具.道具类型ID.Equals("2017062404") || 待检测道具.道具类型ID.Equals("20161126") || 待检测道具.道具类型ID.Equals("943016") || 待检测道具.道具类型ID.Equals("2017021601") || 待检测道具.道具类型ID.Equals("2017021602") || 待检测道具.道具类型ID.Equals("2017061401"))
                    {
                        if (Convert.ToInt32(待检测道具.道具数量) > 10000)
                        {
                            CheatCodeMsg("010");
                            PunishmentProcess(1);
                        }
                    }

                    if (待检测道具.道具类型ID.Equals("2016102001") && Convert.ToInt32(待检测道具.道具数量) > 1000)
                    {
                        CheatCodeMsg("012");
                        PunishmentProcess(2);
                    }
                    if (Convert.ToInt32(待检测道具.道具数量) > 10000000 && !待检测道具.道具类型ID.Equals("2017060302"))
                    {
                        CheatCodeMsg("013");
                        PunishmentProcess(1);
                    }
                    if (待检测道具.道具类型ID.Equals("2017060302") && Convert.ToInt32(待检测道具.道具数量) > 50000000)
                    {
                        CheatCodeMsg("013");
                        PunishmentProcess(2);
                    }
                    if (待检测道具.道具类型ID.Equals("2017021503") && Convert.ToInt32(待检测道具.道具数量) > 50)
                    {
                        CheatCodeMsg("013");
                        PunishmentProcess(2);
                    }
                    if (待检测道具.道具类型ID.Equals("2017092001") && Convert.ToInt32(待检测道具.道具数量) > 50)
                    {
                        CheatCodeMsg("013");
                        PunishmentProcess(1);
                    }
                    if (待检测道具.道具类型ID.Equals("2017080705") && Convert.ToInt32(待检测道具.道具数量) > 100)
                    {
                        CheatCodeMsg("013");
                        PunishmentProcess(2);
                    }*/
                    /*if (Convert.ToInt32(待检测道具.道具类型ID) >= 943029 && Convert.ToInt32(待检测道具.道具类型ID) <= 943038)
                    {
                        if (Convert.ToInt32(待检测道具.道具数量) > 1)
                        {
                            作弊代码通知("011");
                            作弊处理过程(1);
                        }
                    }*/
                }
            }
            catch (Exception ex)
            {
                 throw ex;
            }
        }
        internal static void AntiCheat_C()
        {
            UserInfo 用户 = new DataProcess().ReadUserInfo();
            short grade = Convert.ToInt16(用户.vip);
            int credit = Convert.ToInt32(用户.VIP积分);
            if (grade < 10)
            {
                //如果VIP等级小于10，且有VIP积分
                if (credit > 0)
                {
                    CheatCodeMsg("020");
                    LogSystem.JoinLog(LogSystem.EventKind.作弊检测, "020");
                    PunishmentProcess(2);
                }
                if (用户.至尊VIP)
                {
                    CheatCodeMsg("023");
                    LogSystem.JoinLog(LogSystem.EventKind.作弊检测, "023");
                    PunishmentProcess(2);
                }
                if (用户.星辰VIP)
                {
                    CheatCodeMsg("024");
                    LogSystem.JoinLog(LogSystem.EventKind.作弊检测, "024");
                    PunishmentProcess(2);
                }
            }
            else if (grade == 10)
            {
                if (credit > 10000000)//VIP积分超过10000000判断为作弊
                {
                    CheatCodeMsg("021");
                    LogSystem.JoinLog(LogSystem.EventKind.作弊检测, "021");
                    PunishmentProcess(2);
                }
            }
            else if (grade > 10)
            {
                CheatCodeMsg("022");
                LogSystem.JoinLog(LogSystem.EventKind.作弊检测, "022");
                PunishmentProcess(2);
            }

            if (Convert.ToInt16(用户.道具容量) > 500 && ! new DataProcess().getPower())//检测道具格子
            {
                CheatCodeMsg("B50");
                LogSystem.JoinLog(LogSystem.EventKind.作弊检测, "B50");
                PunishmentProcess(2);
            }

            if (Convert.ToInt16(用户.牧场容量) > 120 && !new DataProcess().getPower())//检测牧场格子
            {
                CheatCodeMsg("B20");
                LogSystem.JoinLog(LogSystem.EventKind.作弊检测, "B20");
                PunishmentProcess(2);
            }
        }
        public static void Cheating_Punishment()
        {
            new DataProcess().ReadUserInfo1();

            //Directory.Delete(DataProcess.TenMinBackup, true);

            File.WriteAllText(FP1, "");
            File.WriteAllText(FP2, "");


            PunishmentProcess(0, 1);
        }
        internal static void Checking_CheatingHistory()
        {
            if (File.Exists(FinalPath) || File.Exists(FP1)|| File.Exists(FP2))
            {
                Cheating_Punishment();
                PunishmentProcess(0, 1);
            }
        }
        
        internal static void PunishmentProcess(int bnum, int 多次 = 0)
        {
            if (bnum != 0)
            {
                UserInfo 用户 = new DataProcess().ReadUserInfo();
                用户.b = (Convert.ToInt16(用户.b) + bnum).ToString();
                new DataProcess().SaveUserDataFile(用户);

                if (Convert.ToInt16(用户.b) >= 2)
                {
                    Cheating_Punishment();
                }
            }
            if (多次 == 0)
            {
                SkTools.AutoClosedMsgBox.Show(Res.RM.GetString("修改器警告"), Res.RM.GetString("严正警告"), 2000);
            }
            else if (多次 == 1)
            {
                SkTools.AutoClosedMsgBox.Show(Res.RM.GetString("多次作弊警告"), Res.RM.GetString("严正警告"), 2000);
            }
            Tools.ForcedExit("作弊或尝试作弊处理");
        }
        internal static void TamperingProcess()
        {
            SkTools.AutoClosedMsgBox.Show(Res.RM.GetString("改配置警告"), Res.RM.GetString("严正警告"), 2000);
            Tools.ForcedExit("篡改配置");
        }
        internal static void CheatCodeMsg(string kind)
        {
            SkTools.AutoClosedMsgBox.Show(Res.RM.GetString("作弊代码") + kind + Res.RM.GetString("误报提示"), Res.RM.GetString("严正警告"), 5000);
        }
    }
}
