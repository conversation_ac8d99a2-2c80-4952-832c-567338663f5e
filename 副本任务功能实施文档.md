# 副本任务功能实施文档

## 1. 功能概述

### 1.1 需求描述
新增副本任务类型，类似于击杀任务，可以指定特定副本和完成次数。玩家需要完成指定副本指定次数才能完成任务。

### 1.2 功能特点
- **指定副本**: 可以指定任意副本地图ID
- **完成次数**: 可以设置需要完成的次数
- **进度跟踪**: 实时跟踪副本完成进度
- **自动检测**: 副本完成后自动更新任务进度
- **任务助手支持**: 支持任务助手自动处理

### 1.3 与现有系统的关系
- **基于击杀任务机制**: 复用击杀任务的进度更新逻辑
- **集成副本系统**: 利用现有的FBROP副本进度管理
- **兼容任务助手**: 支持现有的任务助手自动化功能

## 2. 数据结构设计

### 2.1 任务目标类型扩展

在现有的task类型基础上新增：

```csharp
// 在task.Type中新增类型
public const string TASK_TYPE_FUBEN = "副本";
```

### 2.2 任务目标数据结构

```csharp
// task对象结构（现有）
public class task
{
    public string Type { get; set; }    // 新增值: "副本"
    public string ID { get; set; }      // 副本地图ID (如: "101", "102")
    public string Num { get; set; }     // 需要完成的次数 (如: "5", "10")
    public string inNum { get; set; }   // 当前完成次数 (如: "3", "7")
}
```

### 2.3 副本任务示例

```json
{
    "Type": "副本",
    "ID": "101",        // 副本地图ID
    "Num": "5",         // 需要完成5次
    "inNum": "2"        // 当前已完成2次
}
```

## 3. 核心实现逻辑

### 3.1 副本完成检测机制

**⚠️ 重要：基于冲突分析，副本任务不能在FufillTask中处理，必须在ChangeFbProgress中集成。**

副本任务的核心检测逻辑需要在`ChangeFbProgress`方法中实现，以解决时序冲突问题：

```csharp
// 修改ChangeFbProgress方法，集成副本任务处理
private static void ChangeFbProgress()
{
    if (FBMap && 地图!="地狱之门" && 地图 != "通天塔")
    {
        FBROP 当前层数 = new DataProcess().GetFBROP(地图);

        if (当前层数 != null && new DataProcess().GetAMML(地图).Count <= Convert.ToInt32(当前层数.num) + 1)
        {
            // 关键：在设置-10状态前先更新副本任务
            UpdateFubenTaskProgress(地图);

            new DataProcess().ChangeROP(地图, "-10");
        }
        else
        {
            new DataProcess().PromoteROP(地图);
        }
    }
}

// 新增：副本任务进度更新方法
private static void UpdateFubenTaskProgress(string mapId)
{
    if (!Hell && !TT && !_aj) // 保持与FufillTask相同的条件
    {
        List<TaskInfo> 任务列表 = new DataProcess().GetTasks_PHR();
        bool 有副本任务更新 = false;

        foreach (TaskInfo t in 任务列表)
        {
            if (t.已完成 != "0") // 任务未完成
            {
                foreach (task t1 in t.任务目标)
                {
                    if (t1.Type == "副本" && t1.ID.Equals(mapId))
                    {
                        // 防重复计算：检查是否已经计算过这次完成
                        string 完成标记Key = $"fuben_{mapId}_{DateTime.Now:yyyyMMddHHmm}";
                        if (!已处理副本完成.Contains(完成标记Key))
                        {
                            t1.inNum = (Convert.ToInt32(t1.inNum) + 1).ToString();
                            已处理副本完成.Add(完成标记Key);
                            有副本任务更新 = true;

                            // 发送进度通知
                            int 当前进度 = Convert.ToInt32(t1.inNum);
                            int 目标进度 = Convert.ToInt32(t1.Num);
                            DataProcess.GameForm.发送游戏公告(
                                $"副本任务进度更新：{t.任务名} ({当前进度}/{目标进度})");
                        }
                    }
                }
            }
        }

        if (有副本任务更新)
        {
            new DataProcess().SaveTask_HR_List(任务列表);
            清理过期副本完成标记(); // 清理过期标记
        }
    }
}

// 新增：防重复处理的标记集合
private static HashSet<string> 已处理副本完成 = new HashSet<string>();

// 新增：清理过期标记的方法
private static void 清理过期副本完成标记()
{
    var 当前时间 = DateTime.Now;
    var 过期标记 = 已处理副本完成.Where(mark =>
    {
        var 时间部分 = mark.Split('_').LastOrDefault();
        if (DateTime.TryParseExact(时间部分, "yyyyMMddHHmm", null, DateTimeStyles.None, out DateTime 标记时间))
        {
            return (当前时间 - 标记时间).TotalHours > 1; // 保留1小时内的标记
        }
        return true; // 格式错误的标记直接清理
    }).ToList();

    foreach (var 标记 in 过期标记)
    {
        已处理副本完成.Remove(标记);
    }
}
```

### 3.2 自动重置冲突处理

**⚠️ 关键问题：自动重置会立即覆盖副本完成状态**

自动重置逻辑需要修改以确保副本任务能正确更新：

```csharp
// 修改自动重置逻辑，确保任务更新在重置之前
if (FBMap) {
    var info = new DataProcess().GetFBROP(地图);
    int num = info == null || info.num == null ? 0 : Convert.ToInt32(info.num);
    long tmp = num + 1;

    if (AutoMap && tmp >= MapFloor)
    {
        var r = DataProcess.AutoMap.FirstOrDefault(C => C.mapID.ToString() == 地图);
        if (r == null)
        {
            DataProcess.GameForm.发送红色公告("当前副本不能自动，请联系管理员增加本地图的自动支持。");
        }
        else {
            PropInfo ttkey = new DataProcess().GetAP_ID(r.propID);

            if (ttkey == null || Convert.ToInt32(ttkey.道具数量) < 1)
            {
                DataProcess.GameForm.发送红色公告("钥匙不足，自动副本已结束。");
                AutoMap = false;
                结果.Auto = 2;
            }
            else
            {
                // 关键修改：在重置前确保副本任务已更新
                if (info != null && info.num == "-10")
                {
                    // 副本刚完成，确保任务已更新（双重保险）
                    UpdateFubenTaskProgress(地图);
                }

                new DataProcess().ReviseOrDeletePP(ttkey, 1);
                DataProcess.GameForm.发送红色公告("自动副本功能已帮您自动重置一次副本。");
                new DataProcess().ChangeROP(地图, "-1"); // 重置副本
            }
        }
    }
}
```

## 4. 任务进度计算

### 4.1 进度检查方法扩展

在`DataProcess`类中扩展任务进度检查：

```csharp
// 扩展现有的GetTaskPanelInfo方法
public TaskPanel GetTaskPanelInfo(string 任务序号, bool 检查完成状态 = false)
{
    TaskInfo info = GetAppointedTask_HR(任务序号);
    // ... 现有逻辑 ...
    
    foreach (task 目标 in info.任务目标)
    {
        // 现有类型处理...
        
        // 新增副本任务处理
        if (目标.Type == "副本")
        {
            string 副本名称 = GetMapName(目标.ID); // 需要实现获取地图名称的方法
            目标描述 += $"完成副本【{副本名称}】{目标.Num}次";
            
            int 当前次数 = Convert.ToInt32(目标.inNum);
            int 需要次数 = Convert.ToInt32(目标.Num);
            
            进度描述 += $"({当前次数}/{需要次数})";
            
            if (当前次数 >= 需要次数)
            {
                完成状态 = "0"; // 可完成
            }
            else
            {
                完成状态 = "1"; // 未完成
            }
        }
    }
    
    // ... 返回TaskPanel对象 ...
}
```

### 4.2 地图名称获取方法

```csharp
// 新增方法：根据地图ID获取地图名称
private string GetMapName(string mapId)
{
    try
    {
        MapInfo mapInfo = ReadMapInfo(mapId);
        return mapInfo?.地图名称 ?? $"副本{mapId}";
    }
    catch
    {
        return $"副本{mapId}";
    }
}
```

## 5. 任务助手支持

### 5.1 任务筛选条件扩展

在`PlayerHelper`中扩展任务筛选逻辑：

```csharp
// 在现有的任务筛选逻辑中添加副本任务支持
if (tasks.允许重复 == "1")
{
    bool j1 = true;  // 任务名称检查
    bool j2 = true;  // VIP等级检查
    bool j3 = false; // 是否包含击杀或副本目标
    
    // 任务名称筛选
    if (!tasks.任务名.Contains("【日常】") && !tasks.任务名.Contains("【追龙】"))
    {
        j1 = false;
    }
    
    foreach (task taskaim in tasks.任务目标)
    {
        // VIP等级检查
        if (taskaim.Type == "VIP")
        {
            if (Convert.ToInt16(new DataProcess().ReadUserInfo().vip) < Convert.ToInt16(taskaim.Num))
            {
                j2 = false;
            }
        }
        
        // 支持击杀和副本任务
        if (taskaim.Type == "击杀" || taskaim.Type == "副本")
        {
            j3 = true;
        }
    }
    
    // 只有满足所有条件的任务才能被任务助手处理
    if (j1 && j2 && j3)
    {
        // 添加到任务助手列表
        DataProcess.Task_TaskHelper.Add(tasks);
    }
}
```

## 6. 集成点和调用时机

### 6.1 主要集成点（基于冲突分析优化）

1. **Fight.cs - ChangeFbProgress方法**: 集成副本任务更新逻辑（核心修改）
2. **Fight.cs - 自动重置逻辑**: 确保任务更新在重置之前（关键修改）
3. **DataProcess.cs - GetTaskPanelInfo方法**: 扩展任务进度显示
4. **PlayerHelper.cs**: 扩展任务助手支持
5. **Fight.cs - FufillTask方法**: 保持现有逻辑，不处理副本任务

### 6.2 优化后的调用时机

```
副本战斗胜利
    ↓
ChangeFbProgress() - 检测副本是否完成
    ↓
UpdateFubenTaskProgress() - 在设置-10状态前更新任务
    ↓
设置副本状态为-10（完成）
    ↓
检查自动重置条件
    ↓
（如果需要）确保任务已更新后执行重置
    ↓
FufillTask() - 处理击杀任务等其他任务类型
    ↓
任务助手自动处理（如果配置）
```

### 6.3 关键时序保证

1. **副本任务更新** 在 **副本状态设置为-10** 之前
2. **任务更新确认** 在 **自动重置执行** 之前
3. **防重复机制** 确保同一次完成不被重复计算
4. **异常处理** 确保任何情况下系统都能正常运行

## 7. 错误处理和边界情况

### 7.1 异常情况处理

1. **地图ID不存在**: 跳过该任务，记录警告日志
2. **副本状态异常**: 使用默认值，继续处理
3. **进度数据损坏**: 重置为0，重新开始计算
4. **任务数据不一致**: 验证并修复数据

### 7.2 边界情况

1. **重复完成检测**: 避免同一次副本完成被重复计算
2. **并发访问**: 确保多线程环境下的数据一致性
3. **存档版本兼容**: 兼容旧版本存档格式

## 8. 测试方案

### 8.1 功能测试

1. **基础功能测试**:
   - 创建副本任务
   - 完成指定副本
   - 验证进度更新
   - 完成任务领取奖励

2. **边界测试**:
   - 完成次数为0的任务
   - 不存在的副本ID
   - 异常数据格式

3. **集成测试**:
   - 与击杀任务混合
   - 任务助手自动处理
   - 多个副本任务同时进行

### 8.2 性能测试

1. **大量任务处理**: 测试100+副本任务的性能
2. **频繁副本完成**: 测试快速连续完成副本的处理
3. **内存使用**: 监控内存使用情况

## 9. 部署和配置

### 9.1 配置文件修改

无需修改现有配置文件，副本任务通过任务定义文件配置。

### 9.2 数据库/存档兼容性

- **向后兼容**: 新功能不影响现有存档
- **渐进升级**: 可以逐步添加副本任务
- **回滚支持**: 可以安全回滚到旧版本

### 9.3 部署步骤（基于冲突分析优化）

**阶段1：核心冲突解决**
1. 修改Fight.cs中的ChangeFbProgress方法（集成副本任务更新）
2. 修改Fight.cs中的自动重置逻辑（确保任务更新在重置前）
3. 添加UpdateFubenTaskProgress方法和防重复机制
4. **关键测试**：验证时序问题已解决

**阶段2：功能扩展**
5. 更新DataProcess.cs文件（任务进度计算扩展）
6. 更新PlayerHelper.cs文件（任务助手支持）
7. 更新管理工具（任务创建界面）

**阶段3：测试验证**
8. 时序冲突测试（重点）
9. 自动重置冲突测试（重点）
10. 基础功能测试
11. 任务助手集成测试

**阶段4：配置和发布**
12. 创建副本任务配置
13. 全面回归测试
14. 正式发布

**⚠️ 关键注意事项**：
- 阶段1必须完成并验证无冲突后才能进行后续阶段
- 重点测试时序问题和自动重置冲突
- 确保不影响现有击杀任务功能

---

## 总结

副本任务功能的实施方案充分利用了现有的任务系统和副本管理机制，通过最小化的代码修改实现了完整的功能。主要特点包括：

### 🎯 **核心优势**
1. **无缝集成**: 完全兼容现有任务系统
2. **自动化支持**: 支持任务助手自动处理
3. **实时更新**: 副本完成后立即更新进度
4. **灵活配置**: 支持任意副本和完成次数

### 🔧 **技术特点**
1. **最小侵入**: 复用现有代码结构
2. **高性能**: 利用现有的缓存和优化机制
3. **安全可靠**: 继承现有的加密和验证机制
4. **易于维护**: 遵循现有的代码规范和架构

## 13. 冲突解决方案总结

### 13.1 识别的关键冲突

1. **时序冲突**：FufillTask在ChangeFbProgress之前执行，无法检测到副本完成状态
2. **状态覆盖冲突**：自动重置立即将"-10"状态改为"-1"，副本任务检测失效
3. **重复计算风险**：同一次副本完成可能被多次统计

### 13.2 采用的解决策略

1. **集成式更新**：在ChangeFbProgress中直接处理副本任务，确保时机准确
2. **双重保险机制**：在自动重置前再次确认任务已更新
3. **防重复标记**：使用时间戳标记防止重复计算
4. **最小侵入原则**：不修改现有执行顺序，保持兼容性

### 13.3 优化后的优势

1. **时序准确**：副本任务在副本完成的精确时刻更新
2. **冲突消除**：完全解决了与自动重置的冲突
3. **性能优化**：最小化对现有系统的影响
4. **可靠性提升**：多重保险机制确保功能稳定

### 13.4 风险控制

1. **向后兼容**：不影响现有击杀任务等功能
2. **异常处理**：完善的错误处理和恢复机制
3. **性能监控**：最小化对战斗性能的影响
4. **测试覆盖**：重点测试冲突场景

---

## 结论

经过冲突分析和方案优化，这个副本任务实施方案已经完全解决了时序冲突问题，为游戏增加了丰富的副本任务玩法，同时保持了系统的稳定性和可维护性。

**关键成功因素**：
- ✅ 解决了时序冲突问题
- ✅ 消除了自动重置冲突
- ✅ 保持了系统兼容性
- ✅ 提供了完整的实施指南

## 10. 详细代码实现

### 10.1 Fight.cs 修改（基于冲突分析优化）

**核心修改1：ChangeFbProgress方法集成副本任务处理**

```csharp
// 修改ChangeFbProgress方法，集成副本任务更新
private static void ChangeFbProgress()
{
    if (FBMap && 地图!="地狱之门" && 地图 != "通天塔")
    {
        FBROP 当前层数 = new DataProcess().GetFBROP(地图);

        if (当前层数 != null && new DataProcess().GetAMML(地图).Count <= Convert.ToInt32(当前层数.num) + 1)
        {
            // 关键：在设置-10状态前先更新副本任务
            UpdateFubenTaskProgress(地图);

            new DataProcess().ChangeROP(地图, "-10");
        }
        else
        {
            new DataProcess().PromoteROP(地图);
        }
    }
}

// 新增：副本任务进度更新方法
private static void UpdateFubenTaskProgress(string mapId)
{
    if (!Hell && !TT && !_aj) // 保持与FufillTask相同的条件
    {
        List<TaskInfo> 任务列表 = new DataProcess().GetTasks_PHR();
        bool 有副本任务更新 = false;

        foreach (TaskInfo t in 任务列表)
        {
            if (t.已完成 != "0") // 任务未完成
            {
                foreach (task t1 in t.任务目标)
                {
                    if (t1.Type == "副本" && t1.ID.Equals(mapId))
                    {
                        // 防重复计算：检查是否已经计算过这次完成
                        string 完成标记Key = $"fuben_{mapId}_{DateTime.Now:yyyyMMddHHmm}";
                        if (!已处理副本完成.Contains(完成标记Key))
                        {
                            t1.inNum = (Convert.ToInt32(t1.inNum) + 1).ToString();
                            已处理副本完成.Add(完成标记Key);
                            有副本任务更新 = true;

                            // 发送进度通知
                            int 当前进度 = Convert.ToInt32(t1.inNum);
                            int 目标进度 = Convert.ToInt32(t1.Num);
                            DataProcess.GameForm.发送游戏公告(
                                $"副本任务进度更新：{t.任务名} ({当前进度}/{目标进度})");
                        }
                    }
                }
            }
        }

        if (有副本任务更新)
        {
            new DataProcess().SaveTask_HR_List(任务列表);
            清理过期副本完成标记(); // 清理过期标记
        }
    }
}

// 新增：防重复处理的标记集合
private static HashSet<string> 已处理副本完成 = new HashSet<string>();

// 新增：清理过期标记的方法
private static void 清理过期副本完成标记()
{
    var 当前时间 = DateTime.Now;
    var 过期标记 = 已处理副本完成.Where(mark =>
    {
        var 时间部分 = mark.Split('_').LastOrDefault();
        if (DateTime.TryParseExact(时间部分, "yyyyMMddHHmm", null, DateTimeStyles.None, out DateTime 标记时间))
        {
            return (当前时间 - 标记时间).TotalHours > 1; // 保留1小时内的标记
        }
        return true; // 格式错误的标记直接清理
    }).ToList();

    foreach (var 标记 in 过期标记)
    {
        已处理副本完成.Remove(标记);
    }
}
```

**核心修改2：自动重置逻辑优化**

```csharp
// 修改自动重置部分，确保任务更新在重置之前
if (FBMap) {
    var info = new DataProcess().GetFBROP(地图);
    int num = info == null || info.num == null ? 0 : Convert.ToInt32(info.num);
    long tmp = num + 1;

    if (AutoMap && tmp >= MapFloor)
    {
        var r = DataProcess.AutoMap.FirstOrDefault(C => C.mapID.ToString() == 地图);
        if (r != null)
        {
            PropInfo ttkey = new DataProcess().GetAP_ID(r.propID);

            if (ttkey != null && Convert.ToInt32(ttkey.道具数量) >= 1)
            {
                // 关键修改：在重置前确保副本任务已更新（双重保险）
                if (info != null && info.num == "-10")
                {
                    UpdateFubenTaskProgress(地图);
                }

                new DataProcess().ReviseOrDeletePP(ttkey, 1);
                DataProcess.GameForm.发送红色公告("自动副本功能已帮您自动重置一次副本。");
                new DataProcess().ChangeROP(地图, "-1");
            }
        }
    }
}
```

**重要说明：FufillTask方法保持不变**

```csharp
// FufillTask方法不需要修改，只处理击杀任务等其他类型
private static void FufillTask(UserInfo user)
{
    if (!Hell && !TT && !_aj)
    {
        List<TaskInfo> 任务列表 = new DataProcess().GetTasks_PHR();
        foreach (TaskInfo t in 任务列表)
        {
            if (t.已完成 != "0")
            {
                foreach (task t1 in t.任务目标)
                {
                    // 只处理击杀任务，副本任务由ChangeFbProgress处理
                    if (t1.Type == "击杀")
                    {
                        if (t1.ID.Equals(怪物.形象))
                        {
                            t1.inNum = (Convert.ToInt32(t1.inNum) + 1).ToString();
                        }
                    }
                    // 注意：不在这里处理副本任务
                }
            }
        }

        new DataProcess().SaveTask_HR_List(任务列表);
    }

    // 任务助手逻辑保持不变
    // ...
}
```

### 10.2 DataProcess.cs 扩展

添加副本任务相关的方法：

```csharp
// 新增：获取地图名称
public string GetMapName(string mapId)
{
    try
    {
        MapInfo mapInfo = ReadMapInfo(mapId);
        if (mapInfo != null && !string.IsNullOrEmpty(mapInfo.地图名称))
        {
            return mapInfo.地图名称;
        }

        // 如果没有找到地图信息，返回默认名称
        return $"副本{mapId}";
    }
    catch (Exception ex)
    {
        // 记录错误日志
        LogSystem.JoinLog(LogSystem.EventKind.加入日志, $"获取地图名称失败: {mapId}, 错误: {ex.Message}");
        return $"副本{mapId}";
    }
}

// 扩展：任务进度计算方法
public TaskPanel GetTaskPanelInfo(string 任务序号, bool 检查完成状态 = false)
{
    TaskInfo info = GetAppointedTask_HR(任务序号);
    if (info == null) return null;

    TaskPanel 面板 = new TaskPanel();
    面板.任务名字 = info.任务名;
    面板.位置 = true;

    string 目标描述 = "";
    string 进度描述 = "";
    string 完成状态 = "1"; // 默认未完成
    bool 所有目标完成 = true;

    foreach (task 目标 in info.任务目标)
    {
        bool 当前目标完成 = false;

        // 现有任务类型处理...
        if (目标.Type == "击杀")
        {
            string 怪物名称 = GetMonsterName(目标.ID);
            目标描述 += $"击杀【{怪物名称}】{目标.Num}只";

            int 当前击杀 = Convert.ToInt32(目标.inNum);
            int 需要击杀 = Convert.ToInt32(目标.Num);
            进度描述 += $"({当前击杀}/{需要击杀})";

            当前目标完成 = 当前击杀 >= 需要击杀;
        }
        // 新增副本任务处理
        else if (目标.Type == "副本")
        {
            string 副本名称 = GetMapName(目标.ID);
            目标描述 += $"完成副本【{副本名称}】{目标.Num}次";

            int 当前次数 = Convert.ToInt32(目标.inNum);
            int 需要次数 = Convert.ToInt32(目标.Num);
            进度描述 += $"({当前次数}/{需要次数})";

            当前目标完成 = 当前次数 >= 需要次数;
        }

        if (!当前目标完成)
        {
            所有目标完成 = false;
        }

        目标描述 += " ";
        进度描述 += " ";
    }

    面板.任务目标 = 目标描述.Trim();
    面板.任务进度 = 进度描述.Trim();
    面板.是否完成 = 所有目标完成 ? "0" : "1";

    return 面板;
}

// 新增：验证副本任务配置
public bool ValidateFubenTask(TaskInfo task)
{
    foreach (var 目标 in task.任务目标)
    {
        if (目标.Type == "副本")
        {
            // 验证地图ID是否存在
            if (string.IsNullOrEmpty(目标.ID))
            {
                return false;
            }

            // 验证完成次数是否合理
            if (!int.TryParse(目标.Num, out int 次数) || 次数 <= 0 || 次数 > 1000)
            {
                return false;
            }

            // 验证地图是否为副本类型
            try
            {
                MapInfo mapInfo = ReadMapInfo(目标.ID);
                if (mapInfo == null || mapInfo.Type != "1")
                {
                    return false; // 不是副本地图
                }
            }
            catch
            {
                return false;
            }
        }
    }

    return true;
}
```

## 11. 任务配置示例

### 11.1 副本任务JSON配置

```json
{
    "任务序号": "FB001",
    "任务名": "【日常】初级副本挑战",
    "任务介绍": "完成指定的初级副本来获得丰厚奖励",
    "任务奖励": "金币,50000|经验,10000|道具,2016092304,1",
    "允许重复": "1",
    "网络任务": false,
    "任务目标": [
        {
            "Type": "副本",
            "ID": "101",
            "Num": "3",
            "inNum": "0"
        }
    ]
}
```

### 11.2 混合任务配置

```json
{
    "任务序号": "FB002",
    "任务名": "【日常】副本与击杀组合任务",
    "任务介绍": "完成副本并击杀指定怪物",
    "任务奖励": "金币,100000|元宝,100|道具,2016100403,1",
    "允许重复": "1",
    "网络任务": false,
    "任务目标": [
        {
            "Type": "副本",
            "ID": "102",
            "Num": "2",
            "inNum": "0"
        },
        {
            "Type": "击杀",
            "ID": "1001",
            "Num": "10",
            "inNum": "0"
        }
    ]
}
```

### 11.3 高级副本任务

```json
{
    "任务序号": "FB003",
    "任务名": "【追龙】高级副本挑战",
    "任务介绍": "挑战高难度副本，证明你的实力",
    "任务奖励": "金币,200000|水晶,500|道具,2017080712,1",
    "允许重复": "1",
    "网络任务": false,
    "任务目标": [
        {
            "Type": "VIP",
            "ID": "",
            "Num": "3",
            "inNum": "0"
        },
        {
            "Type": "副本",
            "ID": "201",
            "Num": "5",
            "inNum": "0"
        }
    ]
}
```

## 12. 管理工具扩展

### 12.1 任务管理界面修改

在Admin/任务管理.cs中添加副本任务支持：

```csharp
// 在任务类型下拉框中添加"副本"选项
private void InitializeTaskTypes()
{
    comboBox_TaskType.Items.Clear();
    comboBox_TaskType.Items.Add("击杀");
    comboBox_TaskType.Items.Add("收集");
    comboBox_TaskType.Items.Add("等级");
    comboBox_TaskType.Items.Add("副本");  // 新增
    // ... 其他类型
}

// 副本任务特殊处理
private void comboBox_TaskType_SelectedIndexChanged(object sender, EventArgs e)
{
    string selectedType = comboBox_TaskType.SelectedItem?.ToString();

    if (selectedType == "副本")
    {
        // 显示副本选择控件
        label_TargetID.Text = "副本ID:";
        label_TargetNum.Text = "完成次数:";

        // 加载副本列表到下拉框
        LoadFubenList();
    }
    // ... 其他类型处理
}

private void LoadFubenList()
{
    comboBox_TargetID.Items.Clear();

    try
    {
        // 加载所有副本地图
        var 副本列表 = new DataProcess().GetAllMaps().Where(m => m.Type == "1");

        foreach (var 副本 in 副本列表)
        {
            comboBox_TargetID.Items.Add($"{副本.地图ID} - {副本.地图名称}");
        }
    }
    catch (Exception ex)
    {
        MessageBox.Show($"加载副本列表失败: {ex.Message}");
    }
}
```

### 12.2 任务验证扩展

```csharp
// 扩展任务验证方法
private bool ValidateTaskData()
{
    // ... 现有验证逻辑

    // 副本任务特殊验证
    if (comboBox_TaskType.SelectedItem?.ToString() == "副本")
    {
        // 验证副本ID
        if (string.IsNullOrEmpty(textBox_TargetID.Text))
        {
            MessageBox.Show("请选择副本ID");
            return false;
        }

        // 验证完成次数
        if (!int.TryParse(textBox_TargetNum.Text, out int 次数) || 次数 <= 0 || 次数 > 100)
        {
            MessageBox.Show("完成次数必须是1-100之间的整数");
            return false;
        }

        // 验证副本是否存在
        try
        {
            string 副本ID = textBox_TargetID.Text.Split('-')[0].Trim();
            var mapInfo = new DataProcess().ReadMapInfo(副本ID);
            if (mapInfo == null || mapInfo.Type != "1")
            {
                MessageBox.Show("指定的地图不是副本类型");
                return false;
            }
        }
        catch
        {
            MessageBox.Show("副本ID格式错误或副本不存在");
            return false;
        }
    }

    return true;
}
```
