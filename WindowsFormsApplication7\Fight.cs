﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using PetShikongTools;

namespace Shikong.Pokemon2.PCG
{
    public static class Fight
    {
        internal static int Round;
        internal static PetInfo 怪物;
        internal static PetInfo 宠物;
        internal static string 地图;
        public static string DEBUG_MAP;//只在测试工具中使用
        internal static MonsterInfo 怪物信息;
        internal static bool Hell = false;
        internal static bool TT = false;
        internal static bool WorldBOSS = false;
        internal static MapInfo 地狱通天 = new MapInfo();
        internal static MapInfo 世界BOSS地图信息 = new MapInfo();
        public static string BOSSHP = "";
        internal static string userName = "";
        public static string KillName = "";
        //public static bool BOSSKILL = false;
        internal static bool FBMap = false;
        internal static bool advance = true;
        //internal static bool PK = false;
        internal static double StartTime;
        private static int _Gear;
        internal static bool AutoTT = false;
        internal static int TTFloor;
        internal static bool AutoHell = false;
        internal static int HellFloor;
        internal static bool AutoMap = false;
        internal static int MapFloor;
        internal static Form1 GameForm;
        //飞升丹 涅槃丹 10w钞票 自动战斗券(十次)
        internal static readonly string[] Lwlb1 = {"2016092304", "2016100403", "2016120303", "2016092903"};
        //进化宝石 天界山泉 爱莉雅之卵 雷炎鼠之卵 大师捕捉球 天神灵石 大进化箱
        internal static readonly string[] Lwlb2 =
            {"2016111001", "2016112102", "2016100901", "2016100902", "2016092703", "2016092705", "2016110547"};
        //涅槃兽之卵 百变魔石 BUFF升级书 技能升级书 至尊神石
        internal static readonly string[] Lwlb3 = {"20", "2016112101", "2017021601", "2017021602", "2016100801"};

        /*private static String[] md = { "残酷", "马赛克", "青衫", "洪洪", "一苇以航" };
        private static String[] way = { "骗走了", "抢走了", "偷走了" };*/
        private static string _gwxx = string.Empty;
        private static string _gwmc = string.Empty;
        private static int _times = 0;
        private static bool _aj = false;

        /////////////////////////////
        private static List<string> AMCCfg = new List<string>();
        private static string AMCGL = null;
        private static bool DebugFight = false;//是否在测试模式下

        public static void setDebugFight(bool status)
        {
            if (new DataProcess().getPower()) DebugFight = status;
            else DebugFight = false;

        }


        private static List<string> GetAMCCfg(List<string> 物品列表)
        {
            //这里转为线上配置不走线下了File.Exists(DataProcess.AMC_Path)
            if (string.IsNullOrEmpty(DataProcess.OnlineAllMapDrop))//地图排除全图掉落   && !Hell && !TT && 地图!="23"
            {
                if (AMCCfg == null || AMCCfg.Count == 0 || AMCGL == null)
                {
                    string 全局配置内容 = DataProcess.OnlineAllMapDrop;
                    if (!string.IsNullOrEmpty(全局配置内容))
                    {
                        string[] 全局掉落配置 = SkRC4.DES.DecryptRC4(全局配置内容, new DataProcess().GetKey(1)).Replace(" ", "").Split(',');
                        AMCGL = 全局掉落配置[0];
                        string[] 地图掉落 = 全局掉落配置[1].Split('|');
                        foreach (string 掉落 in 地图掉落)
                        {
                            if (!掉落.Contains(','))
                            {
                                AMCCfg.Add(掉落);
                            }
                            else
                            {
                                string[] 配置 = 掉落.Split(',');
                                for (int i = 1; i <= Convert.ToInt32(配置[1]); i += 1)
                                {
                                    AMCCfg.Add(配置[0]);
                                }
                            }
                        }
                    }
                }

                if (DataProcess.RandomGenerator.Next(1, 101) <= Convert.ToInt32(AMCGL))
                {
                    for (int i = 1; i <= DataProcess.GetProbability(1, 2, 3); i += 1)
                    {
                        物品列表.Add(AMCCfg[DataProcess.RandomGenerator.Next(0, AMCCfg.Count)]);
                    }
                }
            }
            return 物品列表;


            #region 本地版本
            //if (string.IsNullOrEmpty(DataProcess.OnlineAllMapDrop))//地图排除全图掉落   && !Hell && !TT && 地图!="23"
            //{
            //    if (AMCCfg == null || AMCCfg.Count == 0 || AMCGL == null)
            //    {
            //        string 全局配置内容 = new DataProcess().ReadFile(DataProcess.AMC_Path, true);
            //        if (!string.IsNullOrEmpty(全局配置内容))
            //        {
            //            string[] 全局掉落配置 = SkRC4.DES.DecryptRC4(全局配置内容, new DataProcess().GetKey(1)).Replace(" ", "").Split(',');
            //            AMCGL = 全局掉落配置[0];
            //            string[] 地图掉落 = 全局掉落配置[1].Split('|');
            //            foreach (string 掉落 in 地图掉落)
            //            {
            //                if (!掉落.Contains(','))
            //                {
            //                    AMCCfg.Add(掉落);
            //                }
            //                else
            //                {
            //                    string[] 配置 = 掉落.Split(',');
            //                    for (int i = 1; i <= Convert.ToInt32(配置[1]); i += 1)
            //                    {
            //                        AMCCfg.Add(配置[0]);
            //                    }
            //                }
            //            }
            //        }
            //    }

            //    if (DataProcess.RandomGenerator.Next(1, 101) <= Convert.ToInt32(AMCGL))
            //    {
            //        for (int i = 1; i <= DataProcess.GetProbability(1, 2, 3); i += 1)
            //        {
            //            物品列表.Add(AMCCfg[DataProcess.RandomGenerator.Next(0, AMCCfg.Count)]);
            //        }
            //    }
            //}
            //return 物品列表;
            #endregion
        }
        private static List<string> GetMonsterProp(MonsterInfo monsterInfo,List<string> 物品列表)
        {   
            if (monsterInfo != null)
            {
                if (DataProcess.mapPropList != null)
                {
                    var n_mapProp = DataProcess.mapPropList.FirstOrDefault(C => C.map == Fight.地图 && C.monster == monsterInfo.序号);
                    if (n_mapProp != null)
                    {
                        if (string.IsNullOrEmpty(monsterInfo.掉落道具) || monsterInfo.掉落道具 == "无" || monsterInfo.掉落道具 == "0")
                        {
                            if (n_mapProp.props != "") monsterInfo.掉落道具 = "";

                        }
                        if(n_mapProp.props!="") monsterInfo.掉落道具 += "|" + n_mapProp.props;
                        if(n_mapProp.bprops!="" && n_mapProp.bprops != null){
                            物品列表.AddRange(n_mapProp.bprops.Split('|').ToList());
                        }
                            
                    }
                  
                }
                if (!string.IsNullOrEmpty(monsterInfo.掉落道具) && monsterInfo.掉落道具 != "无" && monsterInfo.掉落道具 != "0")
                {   
                    if (monsterInfo.最大掉落 == "0")
                    {
                        monsterInfo.最大掉落 = "1";
                    }


                    int 地图掉落数量 = DataProcess.GetProbability(1, Convert.ToInt32(monsterInfo.最大掉落), 3);
                    List<string> config = new List<string>();
                    string[] 地图掉落 = monsterInfo.掉落道具.Replace(" ", "").Split('|');
                    foreach (string 掉落 in 地图掉落)
                    {
                        if (掉落 != "") {
                            if (!掉落.Contains(','))
                            {
                                config.Add(掉落);
                            }
                            else
                            {
                                string[] 配置 = 掉落.Split(',');
                                for (int i = 1; i <= Convert.ToInt32(配置[1]); i += 1)
                                {
                                    config.Add(配置[0]);
                                }
                            }
                        }
                     
                    }

                    for (int i = 0; i < 地图掉落数量; i++)
                    {
                        物品列表.Add(config[DataProcess.RandomGenerator.Next(0, config.Count)]);
                    }
                }
            }

            return 物品列表;
        }

        /*private static void FBTEST(string text)
        {
            DataProcess.GameForm.发送游戏公告(text);
        }*/

        private static int GetMapExp(MonsterInfo monsterInfo)
        {
            var 经验 = Convert.ToInt32(monsterInfo.经验值);
            
            if (宠物.TalismanState.Contains("额外经验"))
            {   
                //FBTEST("原经验" + 经验.ToString());
                经验 = Convert.ToInt32(经验 * (1 + Convert.ToDouble(宠物.TalismanState.Split('|')[1])));
                //FBTEST("现经验" + 经验.ToString());
            }
            
            return 经验;
        }

        private static int GetMapJB(MapInfo mapinfo)
        {
            int 金币 = 0;
            if (mapinfo.最大金币 != "0")
            {
                金币 = DataProcess.GetProbability(Convert.ToInt32(mapinfo.最小金币), Convert.ToInt32(mapinfo.最大金币), 1);
                
                if (宠物.TalismanState.Contains("额外金币"))
                {   
                    //FBTEST("原金币" + 金币.ToString());
                    金币 = Convert.ToInt32(金币 * (1 + Convert.ToDouble(宠物.TalismanState.Split('|')[1])));
                    //FBTEST("现金币" + 金币.ToString());
                }
                
            }
            return 金币;
        }

        private static int GetMapYB(MapInfo mapinfo)
        {
            int 元宝 = 0;             
            if (mapinfo.最大元宝 != "0" && mapinfo.最大元宝 != "")
            {
                元宝 = DataProcess.GetProbability(Convert.ToInt32(mapinfo.最小元宝), Convert.ToInt32(mapinfo.最大元宝), 2);
            }
            return 元宝;
        }

        private static List<string> GetMapProp(MapInfo mapinfo,List<string> 物品列表)
        {

            if (mapinfo.掉落道具 != "无")
            {
                int 地图掉落数量 = DataProcess.GetProbability(Convert.ToInt32(mapinfo.最小掉落),
                    Convert.ToInt32(mapinfo.最大掉落), 3);
                List<string> config = new List<string>();
                if(mapinfo.掉落道具==null )
                {
                    mapinfo.掉落道具 = "";
                }
                if (DataProcess.mapPropList != null) {
                    var n_mapProp = DataProcess.mapPropList.FirstOrDefault(C=>C.map==mapinfo.地图ID && C.monster=="-1");
                    if (n_mapProp != null) {
                        if (n_mapProp.props != "") mapinfo.掉落道具 += "|" + n_mapProp.props;
                        if (n_mapProp.bprops != "" && n_mapProp.bprops != null)
                        {
                            物品列表.AddRange(n_mapProp.bprops.Split('|').ToList());
                        }

                    }
                } 
                string[] 地图掉落 = mapinfo.掉落道具.Replace(" ", "").Split('|');
              
                foreach (string 掉落 in 地图掉落)
                {
                    if (掉落 != "") {
                        if (!掉落.Contains(','))
                        {
                            config.Add(掉落);
                        }
                        else
                        {
                            string[] 配置 = 掉落.Split(',');
                            for (int i = 1; i <= Convert.ToInt32(配置[1]); i += 1)
                            {
                                config.Add(配置[0]);
                            }
                        }
                    }
                }

                for (int i = 0; i < 地图掉落数量; i++)
                {
                    物品列表.Add(config[DataProcess.RandomGenerator.Next(0, config.Count)]);
                }
            }
            return 物品列表;
        }

        private static List<string> GetVipProp(List<string> 物品列表)//VIP特权 VIP掉落物
        {   
            short vipLv = Convert.ToInt16(new DataProcess().ReadUserInfo().vip);

            if (vipLv > 0 && !Hell && !TT && 地图 != "12" && 地图 != "102" && !FBMap)
            {
                if (DataProcess.RandomGenerator.Next(0, Convert.ToInt32(15 * Math.Sqrt(14 - vipLv))) == 0)
                {
                    int lucky = DataProcess.RandomGenerator.Next(1, 101);
                    {
                        //幸运值1-70
                        if (lucky >= 1 && lucky <= 70)
                        {
                            物品列表.Add(Lwlb1[DataProcess.RandomGenerator.Next(0, Lwlb1.Length)]);
                        }
                        //幸运值71-96
                        else if (lucky >= 71 && lucky <= 96)
                        {
                            物品列表.Add(Lwlb2[DataProcess.RandomGenerator.Next(0, Lwlb2.Length)]);
                        }
                        //幸运值>96
                        else
                        {
                            物品列表.Add(Lwlb3[DataProcess.RandomGenerator.Next(0, Lwlb3.Length)]);
                        }
                    }
                }
            }

            return 物品列表;
        }

        /*private static double[] GetSpeedBuff()
        {
            double[] sb = new double[2];


            double 宠物战斗速度 =
                Math.Round(DataProcess.RandomGenerator.Next(97000, 103001) / 100000.0 * Convert.ToInt64(宠物.速度), 2);
            double 怪物战斗速度 =
                Math.Round(DataProcess.RandomGenerator.Next(97000, 103001) / 100000.0 * Convert.ToInt64(怪物.速度), 2);
            double 速度比 = 宠物战斗速度 / 怪物战斗速度;
            if (速度比 >= 4)
            {
                速度比 = 4;
            }
            else if (速度比 < 4 && 速度比 > 0.25)
            {
            }
            else
            {
                速度比 = 0.25;
            }
            sb[0] = 2 * 速度比 * 速度比 / (速度比 + 1) / (速度比 + 1);
            sb[1] = 2 / (速度比 + 1);
            return sb;
        }*/

        private static void CheckAnJian()
        {
            if (地图 != "12")
            {
                if (怪物.形象.Equals(_gwxx) && 怪物.宠物名字.Equals(_gwmc))
                {
                    _times += 1;
                    if (_times >= 6 && AntiCheat.AnJian)
                    {
                        LogSystem.JoinLog(LogSystem.EventKind.按键精灵, "按键刷怪");
                        _aj = true;
                    }
                    if (_times >= 15)
                    {
                        LogSystem.JoinLog(LogSystem.EventKind.按键精灵, "疑似脚本刷怪");
                        _aj = true;
                    }
                }
                else
                {
                    if (!_aj)
                    {
                        _gwxx = 怪物.形象;
                        _gwmc = 怪物.宠物名字;
                        _times = 1;
                    }
                }

                //Console.WriteLine(地图 + "|" + _gwxx + "|" + _gwmc + "|" + _times + "|" + _aj);
            }
        }


        private static string JoinPropString(List<string> 物品列表)
        {
            string 掉落列表 = string.Empty;

            bool 背包满 = false;
            
            foreach (string s in 物品列表)
            {
                if (string.IsNullOrEmpty(s))
                {
                    continue;
                }

                掉落列表 = 掉落列表 + new DataProcess().GetPropName(s) + "、";

                if (!DebugFight)//非测试下
                {
                    PropInfo prop = new PropInfo { 道具类型ID = s, 道具位置 = PropLoaction.背包.ToString(), 道具数量 = "1" };
                    if (!new DataProcess().AddPlayerProp(prop))
                    {
                        背包满 = true;
                        break;
                    }
                }
               
            }

            掉落列表 = 掉落列表 + ";";
            掉落列表 = 掉落列表.Replace("、;", "");
            if (背包满)
            {
                掉落列表 += "(获取道具失败)";
            }

            return 掉落列表;
        }
        private static List<string> JoinPropString_List(List<string> 物品列表)
        {
            List<string> 掉落列表 = new List<string>();

            bool 背包满 = false;
            foreach (string s in 物品列表)
            {
                if (string.IsNullOrEmpty(s))
                {
                    continue;
                }
                掉落列表.Add(new DataProcess().GetPropName(s)); 
            }
            return 掉落列表;
        }
        //击杀怪物/怪物数量
        private static void FufillTask(UserInfo user)
        {
            if (!Hell && !TT && !_aj)
            {
                List<TaskInfo> 任务列表 = new DataProcess().GetTasks_PHR();
                foreach (TaskInfo t in 任务列表)
                {
                    if (t.已完成 != "0")
                    {
                        foreach (task t1 in t.任务目标)
                        {
                            if (t1.Type == "击杀")
                            {
                                if (t1.ID.Equals(怪物.形象))
                                {
                                    t1.inNum = (Convert.ToInt32(t1.inNum) + 1).ToString();
                                }
                            }
                        }
                    }
                }

                new DataProcess().SaveTask_HR_List(任务列表);
            }

            if (!string.IsNullOrEmpty(user.TaskHelper) && user.TaskHelper.Contains('|'))
            {
                string[] tasks = user.TaskHelper.Split('|');
                foreach (string task in tasks)
                {
                    if (!task.Equals("buy"))
                    {
                        if (new DataProcess().FulfilTask(task))
                        {
                            if (new DataProcess().ReceiveTask(task))
                            {
                                DataProcess.GameForm.发送游戏公告(
                                    "任务助手已自动帮您完成并接取“" + new DataProcess().GetTaskName(task) + "”任务！");
                            }
                        }
                    }
                }
            }
        }



        private static void ChangeFbProgress()//触发地狱卡住
        {
            if (FBMap && 地图!="地狱之门" && 地图 != "通天塔")
            {
                FBROP 当前层数 = new DataProcess().GetFBROP(地图);
                // if (当前层数 != null && new DataProcess().GetAMML(地图).Count <= Convert.ToInt32(当前层数.num) + 1)
                if (当前层数 != null && new DataProcess().GetAMML(地图).Count <= Convert.ToInt32(当前层数.num) + 1)
                {
                    new DataProcess().ChangeROP(地图, "-10");
                }
                else
                {
                    new DataProcess().PromoteROP(地图);
                }
            }
        }
        public static void sb()//重置回合数
        {
            回合数 = 0;
        }
        static double upTime = 0;
        static int 回合数 = 0;
        internal static FightResult 发招(string 技能id)
        {
            回合数++;
            UserInfo user = new DataProcess().ReadUserInfo();
            double endTime = new Tools.GetTime().GetSystemTS();
            FightResult 结果 = new FightResult { 剩余魔法 = Convert.ToInt64(宠物.魔法) };
            AntiCheat.AntiCheat_A();
            //if (WorldBOSS == true && Client.连接状态 == false)
            //{
            //    GameForm.发送游戏公告("网络出现问题,正在重新尝试,请重新进入地图!");
            //    new Client().Client_();
            //}
            //if(WorldBOSS == true && Client.连接状态 == true)
            //{
            //    BOSSHP = new ConvertJson().GetWeb("http://whale-fall.info/sk/BOSSConfig/BOSSHP.ini");
            //    if (Convert.ToInt64(BOSSHP) <= 0)
            //    {
            //        GameForm.发送红色公告("世界BOSS已被玩家[" + Fight.KillName + "]击杀!");
            //        结果.战斗是否结束 = 2;
            //        结果.获得道具 = "世界BOSS已被其他人击败！";
            //        结果.获得金币 = 0;
            //        结果.获得元宝 = 0;
            //        结果.获得经验 = 0;
            //        结果.己方剩余HP = 0;
            //        结果.是否死亡 = 0;
            //        结果.输出 = 0;
            //        结果.advance = 1;
            //        return 结果;
            //    }
            //    if (userName == "")
            //    {
            //        结果.战斗是否结束 = 3;
            //        结果.advance = 1;//该存档未登陆论坛账号!
            //        return 结果;
            //    }
            //}
            //Double num2 = endTime - upTime;
            //DataProcess.GameForm.发送游戏公告("变速检测时间:"+num2.ToString());
            //var ttt = endTime - upTime;
            //Console.WriteLine("战斗间隔毫秒：" + ttt);
            if ((endTime - upTime) < 2500.00 || 怪物 == null)//结束时间-发招时间?<2.5秒  变速检测
            {
                if (Round == 0 && (endTime - upTime) > 1300)//1200  //2025-1-31从1500修改为1300
                {

                }
                else
                {
                    if (!怪物.宠物名字.Contains('§')&&!Program.getDebug()) {
                        结果.获得道具 = "你很喜欢搞事咯?";
                        结果.获得金币 = 0;
                        结果.获得元宝 = 0;
                        结果.获得经验 = 0;
                        结果.己方剩余HP = 0;
                        结果.是否死亡 = 0;
                        结果.战斗是否结束 = 10;
                        结果.输出 = 0;
                        return 结果;
                    }
                    //DataProcess.GameForm.发送红色公告("疑似变速,战斗时间:" + num1.ToString());

                }

            }
            upTime= new Tools.GetTime().GetSystemTS();


            //if (_Gear >= 70)//变速检测  原50  
            //{
            //    SkTools.AutoClosedMsgBox.Show(Res.RM.GetString("变速齿轮提示"), Res.RM.GetString("严正警告"), 2000);
            //    Tools.ForcedExit("变速齿轮战斗检测");
            //}
            double 技能加成 = 1.0;
            double 伤害加深 = Convert.ToDouble(宠物.加深);
            double 伤害抵消 = Convert.ToDouble(宠物.抵消);
            double 吸血 = Convert.ToDouble(宠物.吸血);
            double 吸魔 = Convert.ToDouble(宠物.吸魔);

            //double[] sb = GetSpeedBuff();
            //double 速度加成a = sb[0];
            //double 速度加成b = sb[1];

            foreach (SkillInfo 技能 in 宠物.信息)
            {
                if (技能.技能序号.Equals(技能id))
                {
                    long 耗蓝量 = (Convert.ToInt32(技能.技能等级) + 1) * Convert.ToInt64(技能.信息.耗蓝量);
                    if (Convert.ToInt64(宠物.魔法) < 耗蓝量)
                    {
                    }
                    else
                    {
                        宠物.魔法 = (Convert.ToInt64(宠物.魔法) - 耗蓝量).ToString();
                        SkillConfig 技能配置 = 技能.信息;
                        技能加成 = 1 + Convert.ToDouble(技能配置.技能百分比) + Convert.ToInt32(技能.技能等级) * NumEncrypt.零点零二();//战斗时，主动技能等级加成
                    }
                }
            }

            if (怪物 == null)
            {
                return 结果;
            }

            long 输出=0;
            double 输出加成 = Convert.ToDouble(宠物.命中) - Convert.ToDouble(怪物.闪避);
            //double 基础输出 = Convert.ToDouble(宠物.攻击) - Convert.ToDouble(怪物.防御);

            string 宠物临时攻击 = 宠物.攻击;
            string 怪物临时攻击 = 怪物.攻击;

            if (宠物.TalismanState.Contains("会心一击"))
            {
                string[] cfg = 宠物.TalismanState.Split('|');
                if (DataProcess.RandomGenerator.Next(0, 100) < Convert.ToInt16(cfg[1]))
                {
                    宠物临时攻击 = Convert.ToInt64(Convert.ToInt64(宠物.攻击) * (1 + Convert.ToDouble(cfg[2]))).ToString();
                    //FBTEST("增加" + cfg[2] + "攻击");
                }
            }

            if (宠物.TalismanState.Contains("伤害抵御"))
            {
                string[] cfg = 宠物.TalismanState.Split('|');

                if (DataProcess.RandomGenerator.Next(0, 100) < Convert.ToInt16(cfg[1]))
                {
                    怪物临时攻击 = Convert.ToInt64(Convert.ToInt64(怪物.攻击) * (1 - Convert.ToDouble(cfg[2]))).ToString();
                    //FBTEST("减少" + cfg[2]);
                }
            }


            #region 我方输出 #
            if (输出加成 > 0)
            {
                输出加成 = 输出加成 / Convert.ToDouble(宠物.命中);
                输出加成 = 输出加成 / 2;
                输出加成 = Math.Pow(输出加成, 2);
                输出 = (long)(Convert.ToInt64(宠物临时攻击) * 技能加成 * (1 + 输出加成));
                long 验证 = 输出;
                long 还原 = (long)(验证 / 技能加成 / (1 + 输出加成));
                if (Math.Abs(Convert.ToDouble(宠物临时攻击) - 还原) >= 10)
                {
                    输出 = long.MaxValue;
                }
                else
                {
                    输出 = (long)(输出 * (1.0 + DataProcess.RandomGenerator.Next(-5000, 10001) / 100000.0));
                }            
            }
            else
            {
                输出 = (Convert.ToInt64(宠物临时攻击) - Convert.ToInt64(怪物.防御)) *
                     (1 - (Convert.ToInt64(怪物.闪避) - Convert.ToInt64(宠物.命中)) / Convert.ToInt64(怪物.闪避));
                long 验证 = (Convert.ToInt64(宠物临时攻击) - Convert.ToInt64(怪物.防御)) *
                          (1 - (Convert.ToInt64(怪物.闪避) - Convert.ToInt64(宠物.命中)) / Convert.ToInt64(怪物.闪避));
                long 还原 = 验证 / (1 - (Convert.ToInt64(怪物.闪避) - Convert.ToInt64(宠物.命中)) / Convert.ToInt64(怪物.闪避)) +
                          Convert.ToInt64(怪物.防御);
                if (Math.Abs(Convert.ToDouble(宠物临时攻击) - 还原) >= 10)
                {
                    if (Convert.ToInt64(怪物.成长) - Convert.ToInt64(宠物.成长) >= 5000)
                    {
                        输出 = 1;
                    }
                    else
                    {
                        输出 = long.MaxValue;
                    }
                }
            }

            
            if (输出 != long.MaxValue)
            {
                结果.加深伤害 = (long)(输出 * 伤害加深);
            }
            else if (伤害加深 > 0)
            {
                结果.加深伤害 = -2;
            }

            if (结果.加深伤害 < 0 && 结果.加深伤害 != -2)
            {
                结果.加深伤害 = 1;
            }

            if (伤害加深 <= 0)
            {
                结果.加深伤害 = -1;
            }
            if (输出 < 0 || 怪物.宠物名字.Contains("虚弱-") || 怪物.宠物序号 == "98" || 地图 == "201901")//地图限制-限制输出
            {
                if (地图 == "201901" && user.至尊VIP) //活动地图特权
                {
                    long shuchu = 1 + 1;
                    if (user.星辰VIP)
                    {
                        shuchu = 1 + 1+new DataProcess().ggj(7);
                    }
                    if (怪物.宠物名字.Contains("§"))
                    {
                        if (user.星辰VIP)
                        {
                            shuchu = 1 + 8 + new DataProcess().ggj(2);
                        }
                        else if (user.至尊VIP)
                        {
                            shuchu = 1 + 5;
                        }
                        else
                        {
                            shuchu = 1;
                        }

                    }
                    结果.加深伤害 = 0;
                    输出 = shuchu;
                }
                else
                {
                    结果.加深伤害 = 0;
                    输出 = 1;
                }
                结果.吸血 = 0;
            }
            //地图吸血限制
            if (吸血 <= 0)
            {
                结果.吸血 = 0;
            }
            else
            {
                double 临时吸血 = 吸血;
                if (吸血 > 0 && 吸血 <= 0.4)
                {
                    吸血 = 吸血;
                    if (TT == true)
                    {
                        吸血 *= 0.50;
                    }
                }
                else if (吸血 > 0.4 && 吸血 <= 1.6)
                {
                     
                    if (TT == true)
                    {
                        吸血 *= 0.50;
                    }else 吸血 = NumEncrypt.零点五() * 吸血 + NumEncrypt.零点二();
                }
                else
                {
                    //通天塔 限制吸血
                    if (TT == true)
                    {
                        吸血 *=  0.50;
                    }else 吸血 = Convert.ToDouble(NumEncrypt.一());
                }
                //指定地图的吸血
                if (地图 == "23")
                {
                    吸血 *= NumEncrypt.零点二();
                }
             
                结果.吸血 = (long)(输出 * 吸血);
                if (结果.吸血 <= -10000)
                {
                    结果.吸血 = -2;
                }

                if (Convert.ToInt64(宠物.生命) + 结果.吸血 >= Convert.ToInt64(宠物.最大生命) ||
                    Convert.ToInt64(宠物.生命) + 结果.吸血 <= -100000)
                {
                    宠物.生命 = Convert.ToInt64(宠物.最大生命).ToString();
                }
                else
                {
                    宠物.生命 = (Convert.ToInt64(宠物.生命) + 结果.吸血).ToString();
                }
            }

            if (吸魔 <= 0)
            {
                结果.吸魔 = 0;
            }
            else
            {
                结果.吸魔 = (long)(输出 * 吸魔);
                宠物.魔法 = (Convert.ToInt64(宠物.魔法) + 结果.吸魔).ToString();
            }
            if (地图 == "201901")//赫拉神殿去除吸血
            {
                结果.吸血 = 0;
            }
            #endregion 我方输出结束
            #region 怪物输出 #
            if (Program.getDebug() && new PlayerHelper().getMS())//狗管理特权-管理员特权
            {
                输出 = 1000000000000000;
                怪物临时攻击 = "1";
                结果.输出 = 1000000000000000;
            }
            输出加成 = Convert.ToDouble(怪物.命中) - Convert.ToDouble(宠物.闪避);
            long 怪物输出;
            if (输出加成 > 0)
            {
                输出加成 = 输出加成 / Convert.ToDouble(怪物.命中);
                输出加成 = 输出加成 / 2;
                输出加成 = Math.Pow(输出加成, 1.5);
                怪物输出 = (long)(Convert.ToInt64(怪物临时攻击) * (1 + 输出加成));
                long 验证 = (long)(Convert.ToInt64(怪物临时攻击) * (1 + 输出加成));
                long 还原 = (long)(验证 / (1 + 输出加成));
                if (Math.Abs(Convert.ToDouble(怪物临时攻击) - 还原) >= 10)
                {
                    怪物输出 = long.MaxValue;
                }
            }
            else
            {
                怪物输出 = (Convert.ToInt64(怪物临时攻击) - Convert.ToInt64(宠物.防御)) *
                       (1 - (Convert.ToInt64(宠物.闪避) - Convert.ToInt64(怪物.命中)) / Convert.ToInt64(宠物.闪避));
                long 验证 = 怪物输出;
                long 还原 = 验证 / (1 - (Convert.ToInt64(宠物.闪避) - Convert.ToInt64(怪物.命中)) / Convert.ToInt64(宠物.闪避)) +
                          Convert.ToInt64(宠物.防御);
                if (Math.Abs(Convert.ToDouble(怪物临时攻击) - 还原) >= 10)
                {
                    怪物输出 = long.MaxValue;
                }
            }
            //地图伤害抵消限制
            if (伤害抵消 <= 0)
            {
                结果.抵消伤害 = -1;
            }
            else
            {

                double 临时抵消 = 伤害抵消;
                if (伤害抵消 > 0 && 伤害抵消 <= 0.3)
                {
                    伤害抵消 = 伤害抵消;
                }
                else if (伤害抵消 > 0.3 && 伤害抵消 <= 1)
                {
                    伤害抵消 = NumEncrypt.二点零() * 伤害抵消 / NumEncrypt.七点零() + 0.21428571;
                    if (TT == true) 伤害抵消 = NumEncrypt.二点零() * 伤害抵消 / NumEncrypt.七点零() + 0.16428571;
                }
                else
                {
                    伤害抵消 = NumEncrypt.零点五();
                    if(TT==true) 伤害抵消 = NumEncrypt.零点五()*0.9;
                }
                //指定地图的抵消
                if (地图 == "23")
                {
                    伤害抵消 *= NumEncrypt.零点三();
                }
              
                结果.抵消伤害 = (long)(怪物输出 * 伤害抵消);
            }

            if (结果.抵消伤害 < 0)
            {
                结果.抵消伤害 = 0;
            }


            if (怪物输出 < 0 || 怪物.宠物名字.Contains("弱化-") || 怪物.宠物序号 == "98" || 地图 == "201901")
            {
                结果.抵消伤害 = 0;
                怪物输出 = 1;
            }
            #endregion 计算结束
            结果.advance = advance ? 1 : 0;
            Round += 1;

            宠物.生命 = (Convert.ToInt64(宠物.生命) - 怪物输出 + 结果.抵消伤害).ToString();
            //这边要一个对世界BOSS造成伤害后向服务器发送数据的方法
            if (结果.加深伤害 > 0)
            {
                //if (WorldBOSS)//如果为世界BOSS则向服务器取BOSS剩余HP
                //{
                //    怪物.生命 =BOSSHP;
                //    //怪物.生命 = new ConvertJson().GetWeb("http://whale-fall.info/sk/BOSSConfig/BOSSHP.ini"); 
                //}
                怪物.生命 = (Convert.ToInt64(怪物.生命) - 输出 - 结果.加深伤害).ToString();
                //if(WorldBOSS)
                //{
                //    Int64 ATK= 输出 + 结果.加深伤害;
                //    bool send= new Client().Send_Msg(userName+"|Attack|" + (ATK.ToString()));
                //    if (!send)
                //    {
                //        怪物.生命 = Convert.ToInt64(怪物.生命).ToString();
                //    }
                //}

            }
            else {
                怪物.生命 = (Convert.ToInt64(怪物.生命) - 输出).ToString();
                //if (WorldBOSS)
                //{
                //    Int64 ATK = 输出 + 结果.加深伤害;
                //    bool send = new Client().Send_Msg(userName + "|Attack|" + (ATK.ToString()));
                //    if (!send)
                //    {
                //        怪物.生命 = Convert.ToInt64(怪物.生命).ToString();
                //    }
                //}
            }
            
            if (Convert.ToInt64(宠物.生命) <= 0)
            {
                if (宠物.TalismanState.Contains("神佑复生"))
                {
                    int lucky = DataProcess.RandomGenerator.Next(1, 101);
                    if (lucky == 50)
                    {
                        宠物.生命 = (Convert.ToDouble(宠物.TalismanState.Split('|')[1]) * Convert.ToInt64(宠物.最大生命))
                            .ToString(CultureInfo.InvariantCulture);
                    }

                }
            }
            


            if (Convert.ToInt64(宠物.生命) <= 0)
            {
                结果.战斗是否结束 = 1;
                结果.是否死亡 = 1;
                宠物.生命 = "0";
                结果.对方剩余HP = Convert.ToInt64(怪物.生命);
                结果.己方剩余HP = Convert.ToInt64(宠物.生命);
                结果.受到伤害 = 怪物输出;
                new DataProcess().PetDied(宠物.宠物序号);
                AntiCheat.AntiCheat_A();
                if (!advance)
                {
                    //后手并且宠物被打死了，只记录怪物战斗结果
                    结果.输出 = 0;
                    结果.加深伤害 = 0;
                    结果.吸血 = 0;
                    结果.吸魔 = 0;
                    结果.己方剩余HP = 0;
                    结果.战斗是否结束 = 1;
                    结果.是否死亡 = 1;          
                    宠物.生命 = "0";           
                    

                    if ((AutoHell && Hell) || (AutoTT && TT))
                    {
                        DataProcess.GameForm.发送游戏公告("还没到您设置的层数就打不过啦，自动地狱通天已结束。");
                        AutoTT = false;
                        AutoHell = false;
                        结果.Auto = 1;
                    }
                    
                    //new DataProcess().PetDied(宠物.宠物序号);

                    //结果.是否死亡 = 1;
                }
                else
                {
                    //先手但是被打死了
                    结果.对方剩余HP = Convert.ToInt64(怪物.生命);
                    结果.输出 = 输出;
                    结果.受到伤害 = 怪物输出;
                    怪物 = null;
                    if (FBMap)
                    {
                        new DataProcess().ChangeROP(地图, "-10");
                    }
                    if ((AutoHell && Hell) || (AutoTT && TT))
                    {
                        DataProcess.GameForm.发送游戏公告("还没到您设置的层数就打不过啦，自动地狱通天已结束。");
                        AutoTT = false;
                        AutoHell = false;
                        结果.Auto = 1;
                    }
                }           
            }
            else if(Convert.ToInt64(怪物.生命)<= 0)
            {
                //怪物被打死，只记录宠物战斗结果
                Double num1 = (endTime - StartTime) / Round;
                //Console.WriteLine("变速检测时间:" + num1.ToString());
                //DataProcess.GameForm.发送游戏公告("变速检测时间:" + num1.ToString());
                if ((endTime - StartTime) / Round < 820 || 怪物 == null || AntiCheat.NoDrop)//变速检测   800// 测试最低间隔是900  
                {
                    if (!怪物.宠物名字.Contains('§') && !Program.getDebug())
                    {
                        结果.获得道具 = "你很喜欢搞事咯?";
                        结果.获得金币 = 0;
                        结果.获得元宝 = 0;
                        结果.获得经验 = 0;
                        结果.己方剩余HP = 0;
                        结果.是否死亡 = 0;
                        结果.战斗是否结束 = 10;
                        结果.输出 = 0;
                        _Gear += 1;
                        LogSystem.JoinLog(LogSystem.EventKind.加入日志, "第["+ _Gear + "]次变速检测,时间:" + num1.ToString());
                        return 结果;
                    }
                 
                }
                CheckAnJian();

                var mapinfo = (Hell || TT/*|| WorldBOSS*/) ? 地狱通天 : new DataProcess().ReadMapInfo(地图);
                int fbFightID = -1;
                if (FBMap) {
                    var mapDat = new DataProcess().GetFBROP(地图);

                    if (mapDat != null)
                    {
                        fbFightID = Convert.ToInt32(mapDat.num);
                        if (fbFightID < 0) fbFightID = 0;

                    }
                    else {
                        fbFightID = 0;
                    }

                }
                var monsterInfo = (Hell || TT /*|| WorldBOSS */) ? 怪物信息 : new DataProcess().Get_SMMList1(地图, 怪物.形象, fbFightID);//世界BOSS被击败后

                int 经验 = GetMapExp(monsterInfo);
                int 金币 = GetMapJB(mapinfo);
                int 元宝 = GetMapYB(mapinfo);
                
                //123456
                List<string> 物品列表 = new List<string>();
                if (地图 != "test")
                {
                    物品列表 = GetMapProp(mapinfo, 物品列表);
                    物品列表 = GetMonsterProp(monsterInfo, 物品列表);
                    物品列表 = GetVipProp(物品列表);
                    物品列表 = GetAMCCfg(物品列表);
                }
                int 通天塔层数 = Convert.ToInt32(new DataProcess().ReadUserInfo().TTT);
                UserInfo user1 = new DataProcess().ReadUserInfo();
                //if (KillName.Equals(user1.论坛ID))
                //{
                //    物品列表.Clear();
                //    结果.获得道具 = "BOSS已被别人击杀!";
                //    结果.advance = 2;
                //}
                if (TT && 通天塔层数 % 5 == 0 && 通天塔层数 != 1 )//通天塔指定掉落
                {
                    //物品列表.Add("2019052002");//北冥鸽碎片
                }
                else if (TT && 通天塔层数 % 100 == 0)
                {
                    //物品列表.Add("2017080712");//60CC小神龙
                }
                string 掉落列表 = JoinPropString(物品列表);//战斗胜利添加道具

                


                //new AntiCheat().反作弊();
                结果.获得道具 = 掉落列表;
                结果.获得金币 = 金币;
                结果.获得元宝 = 元宝;
                结果.获得经验 = 经验;        
                结果.是否死亡 = 0;
                结果.战斗是否结束 = 1;
                结果.输出 = 输出;
                结果.受到伤害 = 怪物输出;
                结果.己方剩余HP = Convert.ToInt64(宠物.生命);


                //UserInfo user = new DataProcess().ReadUserInfo();
                if (!Hell && !TT && 地图 != "12" && !FBMap)
                {
                    if (怪物.宠物名字.IndexOf('§') != -1)
                    {
                        user.刷怪数 = 1.ToString();
                        string gg = "成功击杀了BOSS" + 怪物.宠物名字 + ",获得了大量奖励！";
                        DataProcess.GameForm.发送游戏公告("恭喜您" + gg);
                        try
                        {
                            //DataProcess.GameForm.SendData("gg|恭喜玩家【" + DZ.Name + "】" + gg);
                        }
                        catch
                        {
                        }
                    }
                    else
                    {
                        user.刷怪数 = (Convert.ToInt32(user.刷怪数) + 1).ToString();
                        user.每日刷怪数 = (Convert.ToInt32(user.每日刷怪数) + 1).ToString();
                        if (地图 == "201901")
                        {
                            user.hela += 1;
                        }
                        if (地图 == "23")
                        {
                            user.iceland += 1;
                        }
                    }
                }

                user.金币 = (Convert.ToInt64(user.金币) + 金币).ToString();
                user.元宝 = (Convert.ToInt32(user.元宝) + 元宝).ToString();
                //new AntiCheat().反作弊();
                if (Hell)
                {
                    if (String.IsNullOrEmpty(user.地狱层数))
                    {
                        user.地狱层数 = "2";
                    }
                    else
                    {
                        long tmp = Convert.ToInt64(user.地狱层数) + 1;
                        //这里写入历史最高层
                        if (tmp >= Convert.ToInt64(user.历史地狱层数))
                        {
                            user.历史地狱层数 = tmp.ToString();
                        }
                        if (AutoHell && tmp > HellFloor)
                        {
                            PropInfo dykey = new DataProcess().GetAP_ID("2016101705");
                            if (dykey == null || Convert.ToInt32(dykey.道具数量) < 1)
                            {
                                DataProcess.GameForm.发送红色公告("钥匙不足，自动地狱已结束。");
                                AutoHell = false;
                                user.地狱层数 = tmp.ToString();
                                结果.Auto = 2;
                            }
                            else
                            {
                                new DataProcess().ReviseOrDeletePP(dykey, 1);

                                DataProcess.GameForm.发送红色公告("自动地狱功能已帮您自动重置一次地狱之门。");
                                user.地狱层数 = "1";
                            }
                        }
                        else
                        {
                            user.地狱层数 = tmp.ToString();
                        }
                    }
                }

                if (TT)
                {
                    if (String.IsNullOrEmpty(user.TTT))
                    {
                        user.TTT = "2";
                    }
                    else
                    {
                        long tmp = Convert.ToInt64(user.TTT) + 1;
                        if (AutoTT && tmp > TTFloor)
                        {
                            PropInfo ttkey = new DataProcess().GetAP_ID("2018021701");
                            if (ttkey == null || Convert.ToInt32(ttkey.道具数量) < 1)
                            {
                                DataProcess.GameForm.发送红色公告("钥匙不足，自动通天已结束。");
                                AutoTT = false;
                                user.TTT = tmp.ToString();
                                结果.Auto = 2;
                            }
                            else
                            {
                                new DataProcess().ReviseOrDeletePP(ttkey, 1);

                                DataProcess.GameForm.发送红色公告("自动通天功能已帮您自动重置一次通天塔。");
                                user.TTT = "1";
                            }
                        }
                        else
                        {
                            user.TTT = tmp.ToString();
                        }
                    }
                }
                if (FBMap) {
                    var info = new DataProcess().GetFBROP(地图);
                    int num =  info == null||info.num == null ? 0 : Convert.ToInt32(info.num);
                    long tmp = num + 1;
                    if (AutoMap && tmp >= MapFloor)
                    {
                        var r = DataProcess.AutoMap.FirstOrDefault(C => C.mapID.ToString() == 地图);
                        if (r == null)
                        {
                            DataProcess.GameForm.发送红色公告("当前副本不能自动，请联系管理员增加本地图的自动支持。");

                        }
                        else {
                            PropInfo ttkey = new DataProcess().GetAP_ID(r.propID);

                            if (ttkey == null || Convert.ToInt32(ttkey.道具数量) < 1)
                            {
                                DataProcess.GameForm.发送红色公告("钥匙不足，自动副本已结束。");
                                AutoMap = false;

                                结果.Auto = 2;
                            }
                            else
                            {
                                new DataProcess().ReviseOrDeletePP(ttkey, 1);

                                DataProcess.GameForm.发送红色公告("自动副本功能已帮您自动重置一次副本。");

                                new DataProcess().ChangeROP(地图, "-1");
                            }
                        }
                       
                    }
                  
                }
                
                new DataProcess().SaveUserDataFile(user);
                //new DataProcess().RunPropScript("宠物当前经验|" + 经验, out _);
                if(宠物.五行 == "巫") new DataProcess().RunPropScript("巫族宠物经验|" + 经验, out _);
                else new DataProcess().RunPropScript("宠物当前经验|" + 经验, out _);
                FufillTask(user);//怪物计数

                怪物 = null;

                ChangeFbProgress();
            }
            else
            {
              
                if (宠物.TalismanState.Contains("自动回血"))
                {
                    long tmp = Convert.ToInt64(Convert.ToInt64(宠物.最大生命) * Convert.ToDouble(宠物.TalismanState.Split('|')[1]) +
                               Convert.ToInt64(宠物.生命));

                    宠物.生命 = tmp >= Convert.ToInt64(宠物.最大生命) ? 宠物.最大生命 : tmp.ToString();                    
                    //FBTEST("自动回血");
                }

                if (宠物.TalismanState.Contains("自动回蓝"))
                {   
                    long tmp = Convert.ToInt64(Convert.ToInt64(宠物.最大魔法) * Convert.ToDouble(宠物.TalismanState.Split('|')[1]) +
                                               Convert.ToInt64(宠物.魔法));

                    宠物.魔法 = tmp >= Convert.ToInt64(宠物.最大魔法) ? 宠物.最大魔法 : tmp.ToString();
                    //FBTEST("自动回蓝");
                }

                结果.战斗是否结束 = 0;
                结果.是否死亡 = 0;
                结果.对方剩余HP = Convert.ToInt64(怪物.生命);
                结果.己方剩余HP = Convert.ToInt64(宠物.生命);
                结果.输出 = 输出;
                结果.受到伤害 = 怪物输出;

            }
            
            return 结果;
        }
        public static Dictionary<String, int> testProps = new Dictionary<string, int>();
        public static int testNum = 0;
        static List<TJ> tjL = new List<TJ>();
        public static string test(int num,Form1 game)
        {
            System.Threading.Thread thread = new System.Threading.Thread(delegate ()
            {

                System.Diagnostics.Stopwatch sw = new System.Diagnostics.Stopwatch();
                sw.Start();
                List<MonsterInfo> 怪物列表 = new DataProcess().GetAMML(game == null ? DEBUG_MAP : 地图);
                tjL.Clear();
                testProps = new Dictionary<string, int>();
                int 刷怪数 = 1;
                int bn = 0;//遇到指定怪物的次数
                int fbFightID = -1;
                for (int c = 1; c <= num; c++)
                {
                    //Console.WriteLine(c);
                    var mapinfo = (Hell || TT) ? 地狱通天 : new DataProcess().ReadMapInfo(game == null ? DEBUG_MAP : 地图);
                    if (mapinfo.Type == "1")//是否是副本
                    {

                        fbFightID++;
                        if (fbFightID >= 怪物列表.Count) fbFightID = 0;
                    }
                    else
                    {
                        if (!TT && !Hell)
                        {
                            Fight.FBMap = false;

                            刷怪数 += 1;

                            if (刷怪数 < 200)
                            {
                                怪物 = new DataProcess().ChooseRM(怪物列表, 0);
                            }
                            else if (刷怪数 >= 200 && 刷怪数 <= (9112 - new DataProcess().gbs()) ||
                                     刷怪数 > (9112 - new DataProcess().gbs()))
                            {
                                int boss = DataProcess.RandomGenerator.Next(0, 1000);
                                if (boss == 500)
                                {
                                    怪物 = new DataProcess().ChooseRM(怪物列表, 1);
                                    if (怪物.宠物名字.Contains('§')) 刷怪数 = 1;
                                }
                                else 怪物 = new DataProcess().ChooseRM(怪物列表, 0);
                            }
                            else if (刷怪数 > (9112 - new DataProcess().gbs()))
                            {
                                怪物 = new DataProcess().ChooseRM(怪物列表, 1);
                                if (怪物.宠物名字.Contains('§')) 刷怪数 = 1;
                            }

                        }

                    }


                    var monsterInfo = (Hell || TT) ? 怪物信息 : new DataProcess().Get_SMMList1(game == null ? DEBUG_MAP : 地图, 怪物.形象, fbFightID);
                    
                    //if(monsterInfo.怪物名字== "冰波姆")
                    //{
                    //    bn++;
                    //}
                    List<string> 物品列表 = new List<string>();
                    物品列表 = GetMapProp(mapinfo, 物品列表);
                    物品列表 = GetMonsterProp(monsterInfo, 物品列表);
                    物品列表 = GetVipProp(物品列表);
                    物品列表 = GetAMCCfg(物品列表);
                    List<string> 掉落列表 = JoinPropString_List(物品列表);
                    foreach (String p in 掉落列表)
                    {
                        testNum++;
                        if (testProps.ContainsKey(p))
                        {
                            testProps[p]++;
                        }
                        else
                        {
                            testProps.Add(p, 1);
                        }
                    }
                    if (num < 1000)
                    {
                        String tj = "";
                        foreach (var t in testProps)
                        {
                            tj = t.Key + "(" + t.Value + "-" + Math.Round(((Double)t.Value / testNum * 100), 2) + "%)、" + tj;
                        }
                        if(game != null) game.showInfo("本次掉落：" + string.Join(",", 掉落列表) + "<br>总统计：" + tj);
                    }
                    else if(num < 10000)
                    {
                        if (c % 100 == 0)
                        {
                            if (game != null) game.showInfo($"完成第{c / 100}轮100次，当前总共：{c}次，耗时：{sw.ElapsedMilliseconds}ms");
                        }
                    }
                    else if (num < 500000)
                    {
                        if (c % 1000 == 0)
                        {
                            if (game != null) game.showInfo($"完成第{c / 1000}轮1000次，当前总共：{c}次，耗时：{sw.ElapsedMilliseconds}ms");
                        }
                    }
                    else if (num < 5000000)
                    {
                        if (c % 10000 == 0)
                        {
                            if (game != null) game.showInfo($"完成第{c / 1000}轮10000次，当前总共：{c}次，耗时：{sw.ElapsedMilliseconds}ms");
                        }
                    }
                    else
                    {
                        if (c % 100000 == 0)
                        {
                            if (game != null) game.showInfo($"完成第{c / 10000}轮100000次，当前总共：{c}次，耗时：{sw.ElapsedMilliseconds}ms");
                        }
                    }
                }
               //循环结束
                String tj1 = "";
                if (num >= 1000)
                {
                    foreach (var t in testProps)
                    {
                        tj1 = t.Key + "(" + t.Value + "-" + Math.Round(((Double)t.Value / testNum * 100), 2) + "%)、" + tj1;
                    }
                }
                foreach (var p_ in testProps)
                {
                    TJ nP = new TJ();
                    nP.名称 = p_.Key;
                    nP.数量 = p_.Value;
                    tjL.Add(nP);
                }
                string str = $"总统计:{tj1}<br>文件已生成至Z:\\Mapdrop.txt<br>耗时：{sw.ElapsedMilliseconds}ms";
                if (game != null) game.showInfo(str);
                //game.showInfo("累计遇到冰波姆["+bn+"]次");
                try
                {
                    File.WriteAllText(@"Z:\Mapdrop.txt", tj1.Replace("、", "\r\n"), Encoding.Default);
                }
                catch { }
                TestResultFrom TRF = new TestResultFrom(tjL, "掉落查询");
                TRF.ShowDialog();
            });
            thread.Start();
            return "运行完毕";
        }

        public static bool Test_TT = false;
        public static int Test_TTfloor = 1;
        public static string testTT(int num,Form1 game)
        {
            testProps = new Dictionary<string, int>();
            Test_TT = true;
            List<string> 掉落列表 = new List<string>();
            List<string> 物品列表 = new List<string>();
            var mapinfo = (Hell || TT) ? 地狱通天 : new DataProcess().ReadMapInfo(地图);
            for (int c = 1; c <= num; c++)
            {
                Test_TTfloor = 1;
                for (int j = 0; j < 500; j++)
                {
                    掉落列表 = new List<string>();
                    物品列表 = new List<string>();
                    怪物 = new DataProcess().GetTtMonster();
                    var monsterInfo = (Hell || TT) ? 怪物信息 : new DataProcess().Get_SMMList1(地图, 怪物.形象, -1);
                    物品列表 = GetMapProp(mapinfo, 物品列表);
                    物品列表 = GetMonsterProp(monsterInfo, 物品列表);
                    物品列表 = GetVipProp(物品列表);
                    物品列表 = GetAMCCfg(物品列表);
                    掉落列表 = JoinPropString_List(物品列表);
                    foreach (String p in 掉落列表)
                    {
                        testNum++;
                        if (testProps.ContainsKey(p))
                        {
                            testProps[p]++;
                        }
                        else
                        {
                            testProps.Add(p, 1);
                        }
                    }
                    Test_TTfloor++;
                }
                game.showInfo($"已完成{c}次");
            }
            String tj = "";
            foreach (var t in testProps)
            {
                tj = t.Key + "(" + t.Value + "-" + Math.Round(((Double)t.Value / testNum * 100), 2) + "%)、" + tj;
            }
            game.showInfo("<br>总统计：" + tj);
            string str = "总统计:" + tj+ "<br>文件已生成至D:\\TTdrop.txt";
            File.WriteAllText(@"D:\dropPOP.txt", tj, Encoding.Default);
            Test_TT = false;
            return "运行完毕。";
        }
        //internal static 
        internal static string 捕捉宠物(string 道具序号)
        {
            double endTime = new Tools.GetTime().GetSystemTS();
            AntiCheat.AntiCheat_A();
            if (endTime - StartTime < 800.00)
            {
                return "操作太快,请稍后再试.";
            }

            if (Hell || TT || FBMap)
            {
                return "呸,禽兽!这可是副本啊!";
            }

            /*if (PK)
            {
                return "对方的宠物反手就是一巴掌,表示不愿意和你走.";
            }*/
            if (怪物 == null)
            {
                return "怪物已经扑街啦!没必要鞭尸的啦!";
            }

            MonsterType 怪物类型 = new DataProcess().Get_SMT(怪物.形象);
            if (怪物类型.对应的宠物序号 == "0")
            {
                return "你太丑了,他不想和你走!";
            }

            PropInfo 捕捉球 = new DataProcess().GetAP_XH(道具序号);
            if (捕捉球 == null)
            {
                return "捕捉球不够啦!";
            }

            PropConfig 道具信息 = new DataProcess().ReadPropScript(捕捉球.道具类型ID);
            string[] 脚本 = 道具信息.道具脚本.Split('|');
            if (脚本.Length >= 2)
            {
                if (脚本[0].Equals("捕捉道具"))
                {
                    string[] 指定怪物 = {"190107","145","151","146","148","147"};
                    if (Array.IndexOf(指定怪物, 怪物.形象) > -1)//亓玥·宝宝
                    {
                        if (脚本.Length < 3 || 脚本[2] != 怪物.形象)
                        {
                            return "该怪物需要指定捕捉球!";
                        }
                    }
                    int sup = Convert.ToInt32(脚本[1]); //成功百分比
                    double hpp = Convert.ToDouble(怪物.生命) / Convert.ToDouble(怪物.最大生命); //血量百分比
                    if (hpp < NumEncrypt.零点一())
                    {
                        sup += (int) (40 - hpp * NumEncrypt.一百());
                        //var hpp1 = (int)(40 - sup * NumEncrypt.一百());
                        //if(hpp1>31)
                        //{
                        //    hpp1 = 31;
                        //}
                        //hpp += hpp1;
                    }
                    else if (hpp < NumEncrypt.零点三())
                    {
                        sup += 20;
                    }
                    else if (hpp < NumEncrypt.零点四())
                    {
                        sup += 10;
                    }
                    else if (hpp > NumEncrypt.零点七())
                    {
                        sup += -50;
                    }
                    else if (hpp > NumEncrypt.零点六())
                    {
                        sup += -30;
                    }

                    sup -= (int) (Math.Log10(Convert.ToDouble(怪物.成长)) / Math.Log10(4.7) * 10);
                    if (怪物.五行.Equals("神") || 怪物.五行.Equals("神圣"))
                    {
                        sup += -30;
                    }

                    if (sup < 2)
                    {
                        sup = 2;
                    }
                    bool 成功 = false;
                    if (sup >= 100)
                    {
                        成功 = true;
                        //sup = 100;
                    }
                    else
                    {
                        List<int> 成功组 = new List<int>();
                        for (int i = 0; i < sup; i++)
                        {
                            int 随机数 = DataProcess.RandomGenerator.Next(1, 101);
                            while (true)
                            {
                                bool 找到 = false;
                                foreach (int 数字 in 成功组)
                                {
                                    if (数字 == 随机数)
                                    {
                                        找到 = true;
                                    }
                                }

                                if (找到)
                                {
                                    随机数 = DataProcess.RandomGenerator.Next(1, 101);
                                }
                                else
                                {
                                    成功组.Add(随机数);
                                    break;
                                }
                            }
                        }

                        int 随机 = DataProcess.RandomGenerator.Next(1, 101);
                        
                        foreach (int 数字 in 成功组)
                        {
                            if (数字 == 随机)
                            {
                                成功 = true;
                            }
                        }
                    }
                    new DataProcess().ReducePropNum_XH(道具序号);
                    if (怪物.五行.Equals("神") || 怪物.五行.Equals("神圣"))
                    {
                        string[] 可捕捉宠物 = {"106","145", "146", "147", "148", "151", "190107" };
                        if (Array.IndexOf(可捕捉宠物, 怪物.形象)==-1)//可被捕捉的
                        {
                            成功 = false;
                        }
                    }

                    if (成功)
                    {
                        PetInfo pet = new PetInfo();
                        PetConfig 类型 = new DataProcess().GetAppointedPetType(怪物类型.对应的宠物序号);
                        pet.宠物名字 = 类型.宠物名字;
                        pet.形象 = 类型.宠物序号;
                        pet.五行 = 类型.系别;
                        pet.当前经验 = "1";
                        pet.宠物序号 = "1";
                        pet = new DataProcess().SetDefaultAttribute(pet);
                        if (pet.形象.Equals("106"))
                        {
                            pet.成长 = "1";
                        }
                        if (pet.形象.Equals("609"))
                        {
                            pet.成长 = "25";
                        }
                        else
                        {
                            double cc = Convert.ToDouble(怪物.成长);
                            if (cc <= 10)
                            {
                                pet.成长 = (0.1 * Convert.ToDouble(怪物.成长)).ToString(CultureInfo.InvariantCulture);
                            }
                            else if (cc > 10 && cc <= 10000)
                            {
                                pet.成长 = (Math.Log10(Convert.ToDouble(怪物.成长) - 9.99900099) + 4.0004344683).ToString(
                                    CultureInfo.InvariantCulture);
                            }
                            else
                            {
                                pet.成长 = 8.ToString();
                            }
                        }

                        if (new DataProcess().AddPet(pet) == "-1")
                        {
                            return "捕捉失败!玩家最多只能拥有120只宠物!清理一下牧场再试试吧!";
                        }

                        怪物 = null;
                        UserInfo 用户 = new DataProcess().ReadUserInfo();
                        DataProcess.GameForm.发送游戏公告(用户.名字 + "实在是太厉害了!他成功捕捉了【" + pet.宠物名字 + "】!");
                        return "捕捉成功!宠物已经放入你的仓库啦01!";
                    }

                    return "捕捉失败!02";
                }

                return "请选择精灵球噢!";
            }

            return "请选择捕捉球噢!";
        }
    }
}
